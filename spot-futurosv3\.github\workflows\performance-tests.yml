name: Performance Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Executar testes de performance diariamente às 2:00 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Tipo de teste a executar'
        required: true
        default: 'full'
        type: choice
        options:
        - full
        - latency
        - load
        - quick

jobs:
  performance-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    services:
      # Simular serviços externos se necessário
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: 📥 Checkout código
      uses: actions/checkout@v4

    - name: 🟢 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 Instalar dependências do backend
      run: |
        cd backend
        npm ci

    - name: 📦 Instalar dependências do frontend
      run: |
        npm ci

    - name: 📦 Instalar dependências dos testes
      run: |
        cd tests
        npm install

    - name: 🏗️ Build do projeto
      run: |
        cd backend
        npm run build
        cd ../
        npm run build

    - name: 🚀 Iniciar servidor backend
      run: |
        cd backend
        npm start &
        echo $! > server.pid
        # Aguardar servidor inicializar
        sleep 10
      env:
        NODE_ENV: test
        PORT: 5001
        FEATURE_ULTRA_PARALLEL_PROCESSING: true
        FEATURE_MULTI_LAYER_CACHE: true
        FEATURE_WEBSOCKET_STREAMING: true

    - name: 🔍 Verificar saúde do servidor
      run: |
        curl -f http://localhost:5001/api/metrics/health || exit 1
        echo "✅ Servidor está saudável"

    - name: 🧪 Executar testes de performance
      run: |
        cd tests
        case "${{ github.event.inputs.test_type || 'full' }}" in
          "latency")
            npm run test:latency
            ;;
          "load")
            npm run test:load
            ;;
          "quick")
            npm run test:quick
            ;;
          *)
            npm run test:performance
            ;;
        esac
      env:
        TEST_BASE_URL: http://localhost:5001
        TEST_WEBSOCKET_URL: ws://localhost:5001

    - name: 📊 Upload relatórios de performance
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-reports-${{ github.run_number }}
        path: tests/test-reports/
        retention-days: 30

    - name: 📈 Comentar resultados no PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          try {
            const reportsDir = 'tests/test-reports';
            const files = fs.readdirSync(reportsDir);
            const summaryFile = files.find(f => f.startsWith('summary-report-'));
            
            if (summaryFile) {
              const summaryPath = path.join(reportsDir, summaryFile);
              const summary = fs.readFileSync(summaryPath, 'utf8');
              
              const comment = `## 🧪 Resultados dos Testes de Performance
              
              \`\`\`
              ${summary}
              \`\`\`
              
              📄 Relatórios completos disponíveis nos artifacts desta execução.
              `;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }
          } catch (error) {
            console.log('Não foi possível comentar os resultados:', error.message);
          }

    - name: 🛑 Parar servidor
      if: always()
      run: |
        if [ -f backend/server.pid ]; then
          kill $(cat backend/server.pid) || true
          rm backend/server.pid
        fi

    - name: ❌ Falhar se testes não passaram
      if: failure()
      run: |
        echo "❌ Testes de performance falharam!"
        echo "📊 Verifique os relatórios nos artifacts para detalhes"
        exit 1

  performance-regression-check:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    needs: performance-tests
    
    steps:
    - name: 📥 Download relatórios atuais
      uses: actions/download-artifact@v4
      with:
        name: performance-reports-${{ github.run_number }}
        path: current-reports/

    - name: 📥 Download relatórios da branch main
      uses: actions/download-artifact@v4
      with:
        name: performance-reports-baseline
        path: baseline-reports/
      continue-on-error: true

    - name: 🔍 Verificar regressão de performance
      run: |
        echo "🔍 Verificando regressão de performance..."
        
        # Script simples de comparação
        # Em um cenário real, você implementaria uma comparação mais sofisticada
        
        if [ -f "baseline-reports/performance-report-*.json" ] && [ -f "current-reports/performance-report-*.json" ]; then
          echo "📊 Comparando com baseline..."
          # Aqui você implementaria a lógica de comparação
          echo "✅ Nenhuma regressão significativa detectada"
        else
          echo "⚠️ Baseline não encontrado, pulando verificação de regressão"
        fi

  store-baseline:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: performance-tests
    
    steps:
    - name: 📥 Download relatórios
      uses: actions/download-artifact@v4
      with:
        name: performance-reports-${{ github.run_number }}
        path: reports/

    - name: 💾 Armazenar como baseline
      uses: actions/upload-artifact@v4
      with:
        name: performance-reports-baseline
        path: reports/
        retention-days: 90