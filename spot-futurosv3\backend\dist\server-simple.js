import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import { ExchangeService } from './services/ExchangeService.js';
import { CacheService } from './services/CacheService.js';
// Carregar variáveis de ambiente
dotenv.config();
const app = express();
const PORT = process.env.PORT || 3001;
// Middleware
app.use(cors({
    origin: process.env.CORS_ORIGIN || 'http://localhost:5002',
    credentials: true
}));
app.use(express.json());
// Inicializar serviços
const exchangeService = new ExchangeService();
const cacheService = CacheService.getInstance();
// Middleware de logging simples
app.use((req, res, next) => {
    const startTime = Date.now();
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    res.on('finish', () => {
        const latency = Date.now() - startTime;
        if (latency > 1000) {
            console.warn(`🐌 SLOW REQUEST: ${req.method} ${req.path} took ${latency}ms`);
        }
    });
    next();
});
// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '3.0.0'
    });
});
// Opportunities endpoint
app.get('/api/opportunities', async (req, res) => {
    try {
        const startTime = Date.now();
        // Verificar cache primeiro
        const cacheKey = 'opportunities_latest';
        const cached = cacheService.get(cacheKey);
        if (cached) {
            console.log(`✅ Cache hit for opportunities (${Date.now() - startTime}ms)`);
            return res.json({
                success: true,
                data: cached,
                cached: true,
                timestamp: new Date().toISOString()
            });
        }
        // Buscar dados das exchanges
        const opportunities = await exchangeService.calculateArbitrageOpportunitiesUltra();
        const processingTime = Date.now() - startTime;
        // Cachear resultado
        cacheService.set(cacheKey, opportunities, 2000); // 2s TTL
        console.log(`✅ Opportunities calculated in ${processingTime}ms (${opportunities.length} found)`);
        res.json({
            success: true,
            data: opportunities,
            cached: false,
            processingTime,
            count: opportunities.length,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Error fetching opportunities:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch opportunities',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});
// Cache stats endpoint
app.get('/api/cache/stats', (req, res) => {
    try {
        const stats = cacheService.getStats();
        res.json({
            success: true,
            data: stats,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Error getting cache stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get cache stats',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});
// Performance summary endpoint
app.get('/api/performance/summary', (req, res) => {
    try {
        const memUsage = process.memoryUsage();
        const uptime = process.uptime();
        const summary = {
            status: 'HEALTHY',
            uptime: uptime,
            memory: {
                used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
                total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
                percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
            },
            cache: cacheService.getStats(),
            timestamp: Date.now()
        };
        res.json({
            success: true,
            data: summary,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Error getting performance summary:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get performance summary',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});
// Memory health endpoint
app.get('/api/memory/health', (req, res) => {
    try {
        const memUsage = process.memoryUsage();
        const percentage = (memUsage.heapUsed / memUsage.heapTotal) * 100;
        let status = 'HEALTHY';
        let recommendation = 'Memory usage is optimal';
        if (percentage > 90) {
            status = 'CRITICAL';
            recommendation = 'Critical memory usage! Consider restarting the service';
        }
        else if (percentage > 80) {
            status = 'WARNING';
            recommendation = 'High memory usage. Monitor closely';
        }
        res.json({
            success: true,
            data: {
                status,
                percentage: Math.round(percentage),
                used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
                total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
                recommendation
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Error getting memory health:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get memory health',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});
// Quick validation endpoint
app.get('/api/validation/quick', (req, res) => {
    try {
        const memUsage = process.memoryUsage();
        const memoryPercentage = (memUsage.heapUsed / memUsage.heapTotal) * 100;
        const cacheStats = cacheService.getStats();
        const quickValidation = {
            timestamp: Date.now(),
            status: 'HEALTHY',
            checks: {
                memory: memoryPercentage < 80 ? 'PASS' : 'FAIL',
                cache: cacheStats.hitRate > 70 ? 'PASS' : 'FAIL',
                uptime: process.uptime() > 10 ? 'PASS' : 'FAIL'
            },
            metrics: {
                memoryUsage: Math.round(memoryPercentage),
                cacheHitRate: cacheStats.hitRate,
                uptime: Math.round(process.uptime())
            }
        };
        const failedChecks = Object.values(quickValidation.checks).filter(check => check === 'FAIL').length;
        quickValidation.status = failedChecks === 0 ? 'HEALTHY' : failedChecks === 1 ? 'WARNING' : 'CRITICAL';
        res.json({
            success: true,
            data: quickValidation,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Error in quick validation:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to perform quick validation',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});
// Criar servidor HTTP
const server = createServer(app);
// WebSocket Server
const wss = new WebSocketServer({ server });
let connectedClients = 0;
wss.on('connection', (ws) => {
    connectedClients++;
    console.log(`📡 WebSocket client connected. Total: ${connectedClients}`);
    ws.on('close', () => {
        connectedClients--;
        console.log(`📡 WebSocket client disconnected. Total: ${connectedClients}`);
    });
    ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
    });
});
// WebSocket stats endpoint
app.get('/api/websocket/stats', (req, res) => {
    res.json({
        success: true,
        data: {
            connectedClients,
            totalConnections: connectedClients,
            queueLength: 0,
            messagesPerSecond: 0
        },
        timestamp: new Date().toISOString()
    });
});
// Broadcast opportunities via WebSocket
const broadcastOpportunities = async () => {
    try {
        if (connectedClients === 0)
            return;
        const opportunities = await exchangeService.calculateArbitrageOpportunitiesUltra();
        const message = JSON.stringify({
            type: 'opportunities',
            data: opportunities,
            timestamp: Date.now()
        });
        wss.clients.forEach((client) => {
            if (client.readyState === 1) { // WebSocket.OPEN
                client.send(message);
            }
        });
        console.log(`📡 Broadcasted ${opportunities.length} opportunities to ${connectedClients} clients`);
    }
    catch (error) {
        console.error('❌ Error broadcasting opportunities:', error);
    }
};
// Start broadcasting every 1.5 seconds
setInterval(broadcastOpportunities, 1500);
// Start server
server.listen(PORT, () => {
    console.log('🚀 SISTEMA DE ARBITRAGEM - TEMPO REAL');
    console.log('='.repeat(50));
    console.log(`✅ Backend running on http://localhost:${PORT}`);
    console.log(`📡 WebSocket server ready`);
    console.log(`🎯 Target: <2s latency, >80% cache hit rate`);
    console.log('='.repeat(50));
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 Shutting down gracefully...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('🛑 Shutting down gracefully...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
//# sourceMappingURL=server-simple.js.map