import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { ExchangeService } from './services/ExchangeService.js';
import { OpportunityBroadcaster } from './services/OpportunityBroadcaster.js';
import { PerformanceMonitorService } from './services/PerformanceMonitorService.js';
import { MemoryManagerService } from './services/MemoryManagerService.js';
import { WorkerManager } from './workers/WorkerManager.js';
import { UltraFastCache } from './services/UltraFastCache.js';
import { StreamingEngine } from './services/StreamingEngine.js';
import { LoadTestService } from './services/LoadTestService.js';
import { ValidationService } from './services/ValidationService.js';
import { CacheService } from './services/CacheService.js';
// Carregar variáveis de ambiente
dotenv.config();
const app = express();
const PORT = process.env.PORT || 3001;
// Middleware
app.use(cors({
    origin: [
        process.env.CORS_ORIGIN || 'http://localhost:5002',
        'http://localhost:5003',
        'http://localhost:5004'
    ],
    credentials: true
}));
app.use(express.json());
// Rate limiting simples
const requestCounts = new Map();
const RATE_LIMIT_REQUESTS = parseInt(process.env.RATE_LIMIT_REQUESTS || '100');
const RATE_LIMIT_WINDOW = parseInt(process.env.RATE_LIMIT_WINDOW || '60000');
// Middleware de métricas
const metricsMiddleware = async (req, res, next) => {
    const startTime = Date.now();
    const originalSend = res.send;
    res.send = function (data) {
        const responseTime = Date.now() - startTime;
        const isError = res.statusCode >= 400;
        import('./services/MetricsService.js').then(({ MetricsService }) => {
            const metricsService = MetricsService.getInstance();
            metricsService.recordApiCall(responseTime, isError);
        }).catch(console.error);
        return originalSend.call(this, data);
    };
    next();
};
const rateLimiter = (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();
    const clientData = requestCounts.get(clientIP);
    if (!clientData || now > clientData.resetTime) {
        requestCounts.set(clientIP, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
        next();
    }
    else if (clientData.count < RATE_LIMIT_REQUESTS) {
        clientData.count++;
        next();
    }
    else {
        res.status(429).json({
            error: 'Rate limit exceeded',
            message: `Máximo ${RATE_LIMIT_REQUESTS} requests por minuto`
        });
    }
};
app.use(rateLimiter);
app.use(metricsMiddleware);
// Instância do serviço de exchanges
const exchangeService = new ExchangeService();
// 🚀 WORKERS: Initialize ultra-fast worker manager
const workerManager = WorkerManager.getInstance();
// 🚀 ULTRA-CACHE: Initialize multi-layer cache
const ultraCache = UltraFastCache.getInstance();
// 🚀 STREAMING: Initialize streaming engine
const streamingEngine = StreamingEngine.getInstance();
// 🚀 PERFORMANCE: Initialize performance monitoring
const performanceMonitor = PerformanceMonitorService.getInstance();
// 🚀 MEMORY: Initialize memory management
const memoryManager = MemoryManagerService.getInstance();
// 🚀 LOAD TEST: Initialize load testing service
const loadTestService = LoadTestService.getInstance();
// 🚀 VALIDATION: Initialize validation service
const validationService = ValidationService.getInstance();
// 🚀 PERFORMANCE: Middleware para registrar métricas de latência
app.use((req, res, next) => {
    const startTime = Date.now();
    // Log da request
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    // Interceptar o final da response para medir latência
    const originalSend = res.send;
    res.send = function (data) {
        const latency = Date.now() - startTime;
        performanceMonitor.recordLatency(latency);
        // Log de performance se latência alta
        if (latency > 1000) {
            console.warn(`🐌 SLOW REQUEST: ${req.method} ${req.path} took ${latency}ms`);
        }
        return originalSend.call(this, data);
    };
    next();
});
// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        env: {
            enableRealAPIs: process.env.ENABLE_REAL_APIS === 'true',
            nodeEnv: process.env.NODE_ENV || 'development'
        }
    });
});
// Exchange data endpoint
app.get('/api/exchanges/data', async (req, res) => {
    try {
        console.log('🚀 Coletando dados das exchanges...');
        const startTime = performance.now();
        const data = await exchangeService.getAllExchangeDataParallel();
        const processingTime = performance.now() - startTime;
        if (processingTime > 20) {
            console.warn(`🚨 Coleta lenta: ${processingTime.toFixed(1)}ms (target: <20ms)`);
        }
        else {
            console.log(`⚡ Coleta rápida: ${processingTime.toFixed(1)}ms ✅`);
        }
        res.json({
            success: true,
            data,
            processingTime: Math.round(processingTime),
            optimizationLevel: 'ULTRA',
            timestamp: new Date().toISOString(),
            metadata: data.metadata
        });
    }
    catch (error) {
        console.error('❌ Erro na coleta de dados:', error);
        res.status(500).json({
            success: false,
            error: 'Falha na coleta de dados',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// Arbitrage opportunities endpoint
app.get('/api/arbitrage/opportunities', async (req, res) => {
    try {
        console.log('🎯 Calculando oportunidades de arbitragem...');
        const startTime = performance.now();
        // 🚀 ULTRA-FAST: Get opportunities from worker manager
        const opportunities = workerManager.getOpportunities();
        const processingTime = performance.now() - startTime;
        if (processingTime > 100) {
            console.warn(`🚨 Cálculo lento: ${processingTime.toFixed(1)}ms (target: <100ms)`);
        }
        else {
            console.log(`⚡ ${opportunities.length} oportunidades em ${processingTime.toFixed(1)}ms ✅`);
        }
        // ✅ BROADCAST: Enviar todas as oportunidades via WebSocket
        if (opportunities.length > 0) {
            broadcaster.broadcastMultiple(opportunities);
        }
        // ✅ ESTATÍSTICAS: Calcular estatísticas das oportunidades
        const stats = {
            total: opportunities.length,
            avgSpread: opportunities.length > 0
                ? opportunities.reduce((sum, opp) => sum + opp.spreadPercentage, 0) / opportunities.length
                : 0,
            maxSpread: opportunities.length > 0
                ? Math.max(...opportunities.map(opp => opp.spreadPercentage))
                : 0,
            minSpread: opportunities.length > 0
                ? Math.min(...opportunities.map(opp => opp.spreadPercentage))
                : 0,
            positiveOpportunities: opportunities.filter(opp => opp.spreadPercentage > 0).length,
            highVolumeOpportunities: opportunities.filter(opp => (opp.spotVolumeUSDT || 0) > 10000 && (opp.futuresVolumeUSDT || 0) > 10000).length
        };
        res.json({
            success: true,
            opportunities,
            stats, // ✅ Incluir estatísticas
            count: opportunities.length,
            processingTime: Math.round(processingTime),
            optimizationLevel: 'ULTRA',
            timestamp: new Date().toISOString(),
            websocket: {
                connected: broadcaster.getStats().connectedClients,
                broadcasted: opportunities.length
            },
            filters: {
                minSpread: '0.3%',
                maxSpread: '150%',
                minVolumeUSDT: '5000',
                onlyPositiveSpreads: true
            }
        });
    }
    catch (error) {
        console.error('❌ Erro no cálculo de oportunidades:', error);
        res.status(500).json({
            success: false,
            error: 'Falha no cálculo de oportunidades',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 WORKERS: Get worker status
app.get('/api/workers/status', async (req, res) => {
    try {
        const status = workerManager.getStatus();
        res.json({
            success: true,
            data: status,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter status dos workers:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            details: error instanceof Error ? error.message : 'Erro desconhecido'
        });
    }
});
// 🚀 STREAMING: Get streaming engine metrics
app.get('/api/streaming/metrics', async (req, res) => {
    try {
        const metrics = streamingEngine.getMetrics();
        const broadcasterStats = streamingEngine.getBroadcasterStats();
        res.json({
            success: true,
            data: {
                streaming: metrics,
                broadcaster: broadcasterStats
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter métricas do streaming:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            details: error instanceof Error ? error.message : 'Erro desconhecido'
        });
    }
});
// 🚀 STREAMING: Get current filtered opportunities
app.get('/api/streaming/opportunities', async (req, res) => {
    try {
        const opportunities = streamingEngine.getCurrentOpportunities();
        res.json({
            success: true,
            data: opportunities,
            metadata: {
                count: opportunities.length,
                timestamp: new Date().toISOString(),
                source: 'streaming'
            }
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter oportunidades do streaming:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            details: error instanceof Error ? error.message : 'Erro desconhecido'
        });
    }
});
// 🚀 ULTRA-CACHE: Get multi-layer cache statistics
app.get('/api/cache/ultra-stats', async (req, res) => {
    try {
        const stats = ultraCache.getStats();
        res.json({
            success: true,
            data: stats,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter estatísticas do ultra cache:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            details: error instanceof Error ? error.message : 'Erro desconhecido'
        });
    }
});
// Cache stats endpoint
app.get('/api/cache/stats', async (req, res) => {
    try {
        const { CacheService } = await import('./services/CacheService.js');
        const cacheService = CacheService.getInstance();
        const stats = cacheService.getStats();
        res.json({
            success: true,
            data: stats,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter estatísticas do cache:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter estatísticas do cache',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 PERFORMANCE: Performance metrics endpoint
app.get('/api/performance/metrics', async (req, res) => {
    try {
        const currentMetrics = performanceMonitor.getCurrentMetrics();
        res.json({
            success: true,
            data: currentMetrics,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter métricas de performance:', error);
        performanceMonitor.recordError('METRICS_ENDPOINT_ERROR');
        res.status(500).json({
            success: false,
            error: 'Falha ao obter métricas de performance',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 PERFORMANCE: Performance history endpoint
app.get('/api/performance/history', async (req, res) => {
    try {
        const minutes = parseInt(req.query.minutes) || 60;
        const history = performanceMonitor.getMetricsHistory(minutes);
        res.json({
            success: true,
            data: {
                history,
                timeRange: `${minutes} minutes`,
                dataPoints: history.length
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter histórico de performance:', error);
        performanceMonitor.recordError('HISTORY_ENDPOINT_ERROR');
        res.status(500).json({
            success: false,
            error: 'Falha ao obter histórico de performance',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 PERFORMANCE: Performance alerts endpoint
app.get('/api/performance/alerts', async (req, res) => {
    try {
        const alerts = performanceMonitor.getActiveAlerts();
        res.json({
            success: true,
            data: {
                alerts,
                totalAlerts: alerts.length,
                criticalAlerts: alerts.filter(a => a.severity === 'CRITICAL').length,
                highAlerts: alerts.filter(a => a.severity === 'HIGH').length
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter alertas de performance:', error);
        performanceMonitor.recordError('ALERTS_ENDPOINT_ERROR');
        res.status(500).json({
            success: false,
            error: 'Falha ao obter alertas de performance',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 PERFORMANCE: Performance summary endpoint
app.get('/api/performance/summary', async (req, res) => {
    try {
        const summary = performanceMonitor.getPerformanceSummary();
        res.json({
            success: true,
            data: summary,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter resumo de performance:', error);
        performanceMonitor.recordError('SUMMARY_ENDPOINT_ERROR');
        res.status(500).json({
            success: false,
            error: 'Falha ao obter resumo de performance',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 MEMORY: Memory statistics endpoint
app.get('/api/memory/stats', async (req, res) => {
    try {
        const memoryStats = memoryManager.getMemoryStats();
        res.json({
            success: true,
            data: memoryStats,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter estatísticas de memória:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter estatísticas de memória',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 MEMORY: Memory health endpoint
app.get('/api/memory/health', async (req, res) => {
    try {
        const memoryHealth = memoryManager.getMemoryHealth();
        res.json({
            success: true,
            data: memoryHealth,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter saúde da memória:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter saúde da memória',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 LOAD TEST: Start load test endpoint
app.post('/api/loadtest/start', async (req, res) => {
    try {
        const { scenario, baseURL, endpoints } = req.body;
        if (!scenario || !baseURL || !endpoints) {
            return res.status(400).json({
                success: false,
                error: 'Missing required parameters: scenario, baseURL, endpoints',
                timestamp: new Date().toISOString()
            });
        }
        const testId = await loadTestService.startTestScenario(scenario, baseURL, endpoints);
        res.json({
            success: true,
            data: { testId },
            message: `Load test ${scenario} started`,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao iniciar teste de carga:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao iniciar teste de carga',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 LOAD TEST: Stop load test endpoint
app.post('/api/loadtest/stop/:testId', async (req, res) => {
    try {
        const { testId } = req.params;
        const stopped = loadTestService.stopLoadTest(testId);
        if (stopped) {
            res.json({
                success: true,
                message: `Load test ${testId} stopped`,
                timestamp: new Date().toISOString()
            });
        }
        else {
            res.status(404).json({
                success: false,
                error: 'Test not found or already stopped',
                timestamp: new Date().toISOString()
            });
        }
    }
    catch (error) {
        console.error('❌ Erro ao parar teste de carga:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao parar teste de carga',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 LOAD TEST: Get active tests endpoint
app.get('/api/loadtest/active', async (req, res) => {
    try {
        const activeTests = loadTestService.getActiveTests();
        res.json({
            success: true,
            data: activeTests,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter testes ativos:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter testes ativos',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 LOAD TEST: Get test results endpoint
app.get('/api/loadtest/results', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 10;
        const results = loadTestService.getTestResults(limit);
        res.json({
            success: true,
            data: results,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter resultados de teste:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter resultados de teste',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 LOAD TEST: Get specific test result endpoint
app.get('/api/loadtest/results/:testId', async (req, res) => {
    try {
        const { testId } = req.params;
        const result = loadTestService.getTestResult(testId);
        if (result) {
            const validation = loadTestService.validateTestResults(testId);
            res.json({
                success: true,
                data: {
                    result,
                    validation
                },
                timestamp: new Date().toISOString()
            });
        }
        else {
            res.status(404).json({
                success: false,
                error: 'Test result not found',
                timestamp: new Date().toISOString()
            });
        }
    }
    catch (error) {
        console.error('❌ Erro ao obter resultado específico:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter resultado específico',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 VALIDATION: Execute complete validation suite
app.post('/api/validation/execute', async (req, res) => {
    try {
        console.log('🔍 Starting complete system validation...');
        const validationResult = await validationService.executeCompleteValidation();
        res.json({
            success: true,
            data: validationResult,
            message: `Validation completed: ${validationResult.overallStatus}`,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro na validação completa:', error);
        res.status(500).json({
            success: false,
            error: 'Falha na validação completa',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 VALIDATION: Get validation targets
app.get('/api/validation/targets', async (req, res) => {
    try {
        const targets = validationService.getValidationTargets();
        res.json({
            success: true,
            data: targets,
            message: 'Validation targets retrieved',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter targets de validação:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter targets de validação',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 VALIDATION: Quick health validation
app.get('/api/validation/quick', async (req, res) => {
    try {
        // Quick validation without load testing
        const performanceMetrics = performanceMonitor.getCurrentMetrics();
        const memoryHealth = memoryManager.getMemoryHealth();
        const cacheStats = CacheService.getInstance().getStats();
        const quickValidation = {
            timestamp: Date.now(),
            status: 'HEALTHY',
            checks: {
                performance: performanceMetrics ? 'PASS' : 'FAIL',
                memory: memoryHealth.status === 'HEALTHY' ? 'PASS' : 'FAIL',
                cache: cacheStats.hitRate > 70 ? 'PASS' : 'FAIL'
            },
            metrics: {
                latencyP95: performanceMetrics?.latency.p95 || 0,
                memoryUsage: memoryHealth.percentage,
                cacheHitRate: cacheStats.hitRate
            }
        };
        const failedChecks = Object.values(quickValidation.checks).filter(check => check === 'FAIL').length;
        quickValidation.status = failedChecks === 0 ? 'HEALTHY' : failedChecks === 1 ? 'WARNING' : 'CRITICAL';
        res.json({
            success: true,
            data: quickValidation,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro na validação rápida:', error);
        res.status(500).json({
            success: false,
            error: 'Falha na validação rápida',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// WebSocket stats endpoint
app.get('/api/websocket/stats', async (req, res) => {
    try {
        const wsStats = broadcaster.getStats();
        res.json({
            success: true,
            data: {
                ...wsStats,
                uptime: process.uptime(),
                serverTime: new Date().toISOString(),
                performance: {
                    averageLatency: wsStats.averageLatency,
                    messagesPerSecond: wsStats.messagesSent / (process.uptime() || 1),
                    clientsConnected: wsStats.connectedClients,
                    queueHealth: wsStats.queueLength < 100 ? 'healthy' : 'congested'
                }
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter estatísticas do WebSocket:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter estatísticas do WebSocket',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// Metrics health endpoint
app.get('/api/metrics/health', async (req, res) => {
    try {
        const { MetricsService } = await import('./services/MetricsService.js');
        const metricsService = MetricsService.getInstance();
        const healthMetrics = metricsService.getSystemHealth();
        res.json({
            success: true,
            data: healthMetrics,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter métricas de saúde:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter métricas de saúde',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// Advanced latency metrics endpoint
app.get('/api/metrics/advanced-latency', async (req, res) => {
    try {
        const { MetricsService } = await import('./services/MetricsService.js');
        const metricsService = MetricsService.getInstance();
        const advancedMetrics = metricsService.getAdvancedLatencyMetrics();
        res.json({
            success: true,
            data: advancedMetrics,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter métricas avançadas:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter métricas avançadas',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// Performance summary endpoint
app.get('/api/metrics/performance-summary', async (req, res) => {
    try {
        const { MetricsService } = await import('./services/MetricsService.js');
        const metricsService = MetricsService.getInstance();
        const summary = metricsService.getPerformanceSummary();
        res.json({
            success: true,
            data: summary,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter resumo de performance:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter resumo de performance',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// Sliding windows endpoint
app.get('/api/metrics/sliding-windows', async (req, res) => {
    try {
        const { MetricsService } = await import('./services/MetricsService.js');
        const metricsService = MetricsService.getInstance();
        const windows = metricsService.getSlidingWindows();
        res.json({
            success: true,
            data: windows,
            count: windows.length,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter janelas deslizantes:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter janelas deslizantes',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// WebSocket performance metrics endpoint
app.get('/api/metrics/websocket-performance', async (req, res) => {
    try {
        const wsStats = broadcaster.getStats();
        const performanceMetrics = {
            latencyMetrics: {
                average: wsStats.averageLatency,
                current: wsStats.averageLatency,
                target: 10 // ms
            },
            throughputMetrics: {
                messagesPerSecond: wsStats.messagesSent / (process.uptime() || 1),
                totalMessages: wsStats.messagesSent,
                queueLength: wsStats.queueLength
            },
            connectionMetrics: {
                activeConnections: wsStats.connectedClients,
                totalConnections: wsStats.totalConnections,
                uptime: process.uptime()
            },
            healthStatus: {
                overall: wsStats.queueLength < 100 && wsStats.averageLatency < 50 ? 'healthy' : 'degraded',
                queueHealth: wsStats.queueLength < 100 ? 'healthy' : 'congested',
                latencyHealth: wsStats.averageLatency < 50 ? 'healthy' : 'slow'
            }
        };
        res.json({
            success: true,
            data: performanceMetrics,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter métricas de performance WebSocket:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter métricas de performance WebSocket',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// Feature flags endpoints
app.get('/api/feature-flags', async (req, res) => {
    try {
        const { FeatureFlagService } = await import('./services/FeatureFlagService.js');
        const flagService = FeatureFlagService.getInstance();
        const flags = flagService.getAllFlags();
        const statistics = flagService.getStatistics();
        res.json({
            success: true,
            data: {
                flags,
                statistics
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter feature flags:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter feature flags',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
app.get('/api/feature-flags/:flagName', async (req, res) => {
    try {
        const { FeatureFlagService } = await import('./services/FeatureFlagService.js');
        const flagService = FeatureFlagService.getInstance();
        const { flagName } = req.params;
        const evaluation = flagService.evaluateFlag(flagName);
        res.json({
            success: true,
            data: {
                flagName,
                enabled: evaluation.enabled,
                rolloutPercentage: evaluation.rolloutPercentage,
                reason: evaluation.reason,
                metadata: evaluation.metadata
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao avaliar feature flag:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao avaliar feature flag',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// Rollback endpoints
app.get('/api/rollback/triggers', async (req, res) => {
    try {
        const { RollbackService } = await import('./services/RollbackService.js');
        const rollbackService = RollbackService.getInstance();
        const triggers = rollbackService.getTriggers();
        const statistics = rollbackService.getStatistics();
        res.json({
            success: true,
            data: {
                triggers,
                statistics
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter triggers de rollback:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter triggers de rollback',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
app.get('/api/rollback/history', async (req, res) => {
    try {
        const { RollbackService } = await import('./services/RollbackService.js');
        const rollbackService = RollbackService.getInstance();
        const history = rollbackService.getHistory();
        res.json({
            success: true,
            data: history,
            count: history.length,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter histórico de rollback:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter histórico de rollback',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
app.post('/api/rollback/manual', async (req, res) => {
    try {
        const { actions, reason } = req.body;
        if (!actions || !Array.isArray(actions) || actions.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Ações de rollback são obrigatórias',
                message: 'Forneça um array de ações para executar o rollback',
                timestamp: new Date().toISOString()
            });
        }
        const { RollbackService } = await import('./services/RollbackService.js');
        const rollbackService = RollbackService.getInstance();
        const result = await rollbackService.executeManualRollback(actions, reason || 'Manual rollback');
        res.json({
            success: true,
            data: result,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao executar rollback manual:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao executar rollback manual',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// Dashboard metrics endpoint
app.get('/api/metrics/dashboard', async (req, res) => {
    try {
        const { MetricsService } = await import('./services/MetricsService.js');
        const metricsService = MetricsService.getInstance();
        const dashboardData = {
            realTime: {
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                memory: process.memoryUsage()
            },
            performance: metricsService.getPerformanceSummary(),
            health: metricsService.getSystemHealth(),
            websocket: broadcaster.getStats()
        };
        res.json({
            success: true,
            data: dashboardData,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter dados do dashboard:', error);
        res.status(500).json({
            success: false,
            error: 'Falha ao obter dados do dashboard',
            message: error instanceof Error ? error.message : 'Erro desconhecido',
            timestamp: new Date().toISOString()
        });
    }
});
// 🚀 ULTRA-WEBSOCKET: Initialize broadcaster
const broadcaster = new OpportunityBroadcaster();
// 🚀 ULTRA-WEBSOCKET: WebSocket connection handling
const server = createServer(app);
broadcaster.initialize(server);
// 🚀 CRÍTICO: Cache para evitar recálculo constante
let cachedOpportunities = [];
let lastCalculation = 0;
const CACHE_DURATION = parseInt(process.env.BROADCAST_CACHE_DURATION || '1500'); // 1.5s cache for real-time
// 🚀 ULTRA-REAL-TIME: Auto-broadcast opportunities every 1-2 seconds for real-time
const BROADCAST_INTERVAL = parseInt(process.env.BROADCAST_INTERVAL || '1500'); // 1.5s interval
setInterval(async () => {
    try {
        if (broadcaster.getStats().connectedClients > 0) {
            console.log('🔄 Auto-broadcasting opportunities to connected clients...');
            const startTime = performance.now();
            // 🚀 CRÍTICO: Usar cache se disponível
            let opportunities;
            if (Date.now() - lastCalculation < CACHE_DURATION && cachedOpportunities.length > 0) {
                opportunities = cachedOpportunities;
                console.log('⚡ Usando cache - 0ms');
            }
            else {
                opportunities = await exchangeService.calculateArbitrageOpportunitiesUltra();
                cachedOpportunities = opportunities;
                lastCalculation = Date.now();
            }
            if (opportunities.length > 0) {
                // 🚀 ULTRA-OTIMIZADO: Filtrar apenas oportunidades de alta qualidade
                const relevantOpportunities = opportunities.filter(opp => {
                    const absSpread = Math.abs(opp.spreadPercentage);
                    return absSpread > 0.2 && // Apenas spreads > 0.5%
                        absSpread < 50 && // Filtrar spreads irreais
                        opp.spotVolume > 1000; // Volume mínimo
                });
                if (relevantOpportunities.length > 0) {
                    // Broadcast all relevant opportunities at once for better performance
                    broadcaster.broadcastMultiple(relevantOpportunities);
                    const processingTime = performance.now() - startTime;
                    console.log(`📡 Auto-broadcast: ${relevantOpportunities.length}/${opportunities.length} opportunities sent to ${broadcaster.getStats().connectedClients} clients in ${processingTime.toFixed(1)}ms`);
                }
            }
        }
    }
    catch (error) {
        console.error('❌ Error in auto-broadcast:', error);
    }
}, BROADCAST_INTERVAL); // 🚀 OTIMIZADO: Configurable interval for real-time updates
// Iniciar servidor
server.listen(PORT, async () => {
    console.log(`🚀 Servidor backend iniciado na porta ${PORT}`);
    console.log(`🔗 Health check: http://localhost:${PORT}/health`);
    console.log(`📡 WebSocket disponível para conexões`);
    console.log(`🎯 Ambiente: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔑 APIs reais: ${process.env.ENABLE_REAL_APIS === 'true' ? 'ATIVADAS' : 'DESATIVADAS'}`);
    // 🚀 ULTRA-FAST: Start all workers
    console.log('🔥 Iniciando workers ultra-rápidos...');
    await workerManager.start();
    console.log('✅ Todos os workers iniciados com sucesso!');
    // 🚀 STREAMING: Start streaming engine
    console.log('🔥 Iniciando streaming engine...');
    await streamingEngine.start();
    console.log('✅ Streaming engine iniciado com sucesso!');
    // ✅ SISTEMA DE ATUALIZAÇÃO AUTOMÁTICA
    console.log('🔄 Iniciando sistema de atualização automática...');
    const updateInterval = setInterval(async () => {
        try {
            const opportunities = workerManager.getOpportunities();
            if (opportunities.length > 0) {
                // Broadcast para todos os clientes WebSocket conectados
                broadcaster.broadcastMultiple(opportunities);
                console.log(`🔄 Auto-update: ${opportunities.length} oportunidades enviadas para ${broadcaster.getStats().connectedClients} clientes`);
            }
        }
        catch (error) {
            console.error('❌ Erro na atualização automática:', error);
        }
    }, 3000); // ✅ Atualizar a cada 3 segundos
    // Cleanup do intervalo no shutdown
    process.on('SIGTERM', () => {
        clearInterval(updateInterval);
    });
    process.on('SIGINT', () => {
        clearInterval(updateInterval);
    });
    console.log('✅ Sistema de atualização automática iniciado (3s)');
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 Recebido SIGTERM, encerrando servidor...');
    broadcaster.cleanup();
    server.close(() => {
        console.log('✅ Servidor encerrado graciosamente');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('🛑 Recebido SIGINT, encerrando servidor...');
    broadcaster.cleanup();
    server.close(() => {
        console.log('✅ Servidor encerrado graciosamente');
        process.exit(0);
    });
});
// 🚀 ULTRA-WEBSOCKET: Export broadcaster for use in other modules
export { broadcaster };
export default app;
//# sourceMappingURL=server.js.map