export class CacheService {
    static instance;
    // 🚀 ULTRA-ADVANCED: 3-Layer Cache Architecture with intelligent promotion
    hotCache = new Map(); // L1: <1ms access, 1s TTL, critical data
    warmCache = new Map(); // L2: <10ms access, 3s TTL, frequent data
    coldCache = new Map(); // L3: <50ms access, 8s TTL, stable data
    // 🚀 CACHE LIMITS: Prevent memory overflow with LRU eviction
    HOT_CACHE_LIMIT = 100;
    WARM_CACHE_LIMIT = 500;
    COLD_CACHE_LIMIT = 1000;
    // Enhanced metrics with 3-layer tracking
    metrics = {
        hotHits: 0,
        warmHits: 0,
        coldHits: 0,
        totalMisses: 0,
        hitRate: 0,
        promotions: 0,
        evictions: 0
    };
    // Legacy support
    cache = new Map();
    defaultTTL = parseInt(process.env.CACHE_TTL || '5000'); // 5s default (optimized for real APIs)
    hitCount = 0;
    missCount = 0;
    /**
     * 🚀 ULTRA-FAST: Simplified singleton for maximum speed
     */
    static getInstance() {
        if (!CacheService.instance) {
            CacheService.instance = new CacheService();
            // 🚀 ULTRA-CLEANUP: Simple cleanup every 5 seconds
            setInterval(() => {
                CacheService.instance.ultraFastCleanup();
            }, 5000);
            console.log('🚀 ULTRA-FAST: 2-layer cache initialized for <10ms access');
        }
        return CacheService.instance;
    }
    /**
     * 🚀 ULTRA-FAST: 3-layer cache with intelligent promotion
     * Target: <1ms hot, <10ms warm, <50ms cold
     */
    getUltraFast(key) {
        // L1: Hot cache: <1ms access
        if (this.hotCache.has(key)) {
            this.metrics.hotHits++;
            this.hitCount++;
            return this.hotCache.get(key);
        }
        // L2: Warm cache: <10ms access
        const warmEntry = this.warmCache.get(key);
        if (warmEntry && !this.isExpired(warmEntry)) {
            // Promote to hot cache for faster future access
            this.promoteToHot(key, warmEntry.data);
            this.metrics.warmHits++;
            this.hitCount++;
            return warmEntry.data;
        }
        // L3: Cold cache: <50ms access
        const coldEntry = this.coldCache.get(key);
        if (coldEntry && !this.isExpired(coldEntry)) {
            // Promote to warm cache for faster future access
            this.promoteToWarm(key, coldEntry.data);
            this.metrics.coldHits++;
            this.hitCount++;
            return coldEntry.data;
        }
        // Cache miss across all layers
        this.metrics.totalMisses++;
        this.missCount++;
        return null;
    }
    /**
     * 🚀 ULTRA-FAST: Simple 2-layer caching with optimized TTL
     */
    setUltraFast(key, data, isHot = false) {
        if (isHot) {
            // Hot cache: 2s TTL (increased for better hit rate)
            this.hotCache.set(key, data);
            // Auto-expire after 2s
            setTimeout(() => {
                this.hotCache.delete(key);
            }, 2000);
        }
        else {
            // Warm cache: 5s TTL (increased for better hit rate)
            const entry = {
                data,
                timestamp: Date.now(),
                ttl: 5000
            };
            this.warmCache.set(key, entry);
        }
    }
    /**
     * 🚀 PROMOTION: Move data to hot cache with LRU eviction
     */
    promoteToHot(key, data) {
        // Check if hot cache is full
        if (this.hotCache.size >= this.HOT_CACHE_LIMIT) {
            // LRU eviction: Remove oldest entry
            const firstKey = this.hotCache.keys().next().value;
            this.hotCache.delete(firstKey);
            this.metrics.evictions++;
        }
        this.hotCache.set(key, data);
        this.metrics.promotions++;
        // Auto-expire from hot cache after 1s
        setTimeout(() => {
            this.hotCache.delete(key);
        }, 1000);
    }
    /**
     * 🚀 PROMOTION: Move data to warm cache with LRU eviction
     */
    promoteToWarm(key, data) {
        // Check if warm cache is full
        if (this.warmCache.size >= this.WARM_CACHE_LIMIT) {
            // LRU eviction: Remove oldest entry
            const firstKey = this.warmCache.keys().next().value;
            this.warmCache.delete(firstKey);
            this.metrics.evictions++;
        }
        this.warmCache.set(key, {
            data,
            timestamp: Date.now(),
            ttl: 3000 // 3s TTL
        });
        this.metrics.promotions++;
    }
    /**
     * 🚀 ULTRA-FAST: Primary get method
     */
    get(key) {
        return this.getUltraFast(key);
    }
    /**
     * 🚀 ULTRA-FAST: Primary set method
     */
    set(key, data, ttl = this.defaultTTL) {
        const isHot = ttl <= 1000; // <1s = hot cache
        this.setUltraFast(key, data, isHot);
        // Also set in legacy cache for compatibility
        const entry = {
            data,
            timestamp: Date.now(),
            ttl
        };
        this.cache.set(key, entry);
    }
    /**
     * 🚀 ULTRA-FAST: Remove from both cache levels
     */
    delete(key) {
        let deleted = false;
        if (this.hotCache.delete(key))
            deleted = true;
        if (this.warmCache.delete(key))
            deleted = true;
        if (this.cache.delete(key))
            deleted = true;
        return deleted;
    }
    /**
     * 🚀 ULTRA-FAST: Clear all cache levels
     */
    clear() {
        this.hotCache.clear();
        this.warmCache.clear();
        this.cache.clear();
        // Reset metrics
        this.metrics = {
            hotHits: 0,
            warmHits: 0,
            totalMisses: 0,
            hitRate: 0
        };
        console.log('🧹 ULTRA-FAST: All cache levels cleared');
    }
    /**
     * 🚀 ULTRA-FAST: Simple cleanup for 2-layer cache
     */
    ultraFastCleanup() {
        const now = Date.now();
        let evicted = 0;
        // Clean warm cache
        for (const [key, entry] of this.warmCache.entries()) {
            if (this.isExpired(entry)) {
                this.warmCache.delete(key);
                evicted++;
            }
        }
        // Clean legacy cache
        this.cleanup();
        // Update hit rate including all 3 cache layers
        const totalRequests = this.metrics.hotHits + this.metrics.warmHits + this.metrics.coldHits + this.metrics.totalMisses;
        if (totalRequests > 0) {
            this.metrics.hitRate = ((this.metrics.hotHits + this.metrics.warmHits + this.metrics.coldHits) / totalRequests) * 100;
        }
        if (evicted > 0) {
            console.log(`🧹 ULTRA-FAST: Cleaned ${evicted} expired entries`);
        }
    }
    /**
     * 🚀 ULTRA-FAST: Simple cleanup method
     */
    cleanup() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()) {
            if (this.isExpired(entry)) {
                this.cache.delete(key);
            }
        }
    }
    /**
     * 🚀 ULTRA-FAST: Check if entry is expired
     */
    isExpired(entry) {
        return (Date.now() - entry.timestamp) > entry.ttl;
    }
    /**
     * 🚀 ULTRA-SMART: Dynamic TTL based on data volatility and type
     */
    getDynamicTTL(key, data) {
        // 🚀 INTELLIGENT: Analyze data to determine optimal TTL based on volatility
        if (key.includes('opportunities')) {
            // Opportunities change frequently - adaptive TTL based on market volatility
            if (Array.isArray(data) && data.length > 0) {
                const avgSpread = data.reduce((sum, opp) => sum + Math.abs(opp.spreadPercentage || 0), 0) / data.length;
                // Higher volatility = shorter TTL for fresher data
                if (avgSpread > 2)
                    return 1000; // High volatility: 1s
                if (avgSpread > 1)
                    return 1500; // Medium volatility: 1.5s
                return 2000; // Low volatility: 2s
            }
            return 1500; // Default for opportunities
        }
        if (key.includes('exchange_data')) {
            // Exchange data is moderately volatile - longer TTL for stability
            return 3000; // 3s for balance of speed/freshness
        }
        if (key.includes('spot_') || key.includes('futures_')) {
            // Individual market data - medium volatility
            return 2000; // 2s for individual markets
        }
        if (key.includes('stats') || key.includes('health')) {
            // System stats change slowly
            return 10000; // 10s for system data
        }
        if (key.includes('cache_stats')) {
            // Cache stats for monitoring
            return 5000; // 5s for cache stats
        }
        // Default for unknown data types - more conservative
        return Math.max(this.defaultTTL, 1000); // Minimum 1s
    }
    /**
     * 🚀 ULTRA-SMART: Set with dynamic TTL based on volatility
     */
    setDynamic(key, data) {
        const ttl = this.getDynamicTTL(key, data);
        this.set(key, data, ttl);
        // 🚀 DEBUG: Log TTL decisions for optimization
        if (process.env.NODE_ENV === 'development') {
            console.log(`🔄 Cache: ${key} → TTL: ${ttl}ms`);
        }
    }
    /**
     * 🚀 ULTRA-INTELLIGENT: Set with intelligent caching strategy
     * Automatically determines best cache layer and TTL
     */
    setUltraIntelligent(key, data, priority = 'warm') {
        const ttl = this.getDynamicTTL(key, data);
        // Determine cache layer based on priority and TTL
        if (priority === 'hot' || ttl <= 1000) {
            // Hot cache for critical data
            this.setUltraFast(key, data, true);
        }
        else if (priority === 'warm' || ttl <= 5000) {
            // Warm cache for frequent data
            this.setUltraFast(key, data, false);
        }
        else {
            // Cold cache for stable data
            this.set(key, data, ttl);
        }
        // Update metrics
        this.metrics.totalSets++;
        // 🚀 DEBUG: Log intelligent caching decisions
        if (process.env.NODE_ENV === 'development') {
            console.log(`🧠 Intelligent Cache: ${key} → ${priority} (TTL: ${ttl}ms)`);
        }
    }
    /**
     * 🚀 ULTRA-FAST: Set data with hot cache priority
     */
    setUltraFast(key, data, isHot = true) {
        const ttl = isHot ? 60000 : 30000; // Hot cache lasts longer
        // Direct cache storage without recursion
        const cacheEntry = {
            data,
            timestamp: Date.now(),
            ttl,
            accessCount: 1,
            lastAccess: Date.now()
        };
        this.cache.set(key, cacheEntry);
        this.metrics.sets++;
        if (isHot) {
            this.promoteToHot(key, data);
        }
    }
    /**
     * 🚀 ULTRA-FAST: Get data with priority handling
     */
    getUltraFast(key) {
        // Direct cache access without recursion
        const entry = this.cache.get(key);
        if (!entry) {
            this.metrics.misses++;
            return null;
        }
        // Check TTL
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            this.metrics.misses++;
            return null;
        }
        // Update access metrics
        entry.accessCount++;
        entry.lastAccess = Date.now();
        this.metrics.hits++;
        return entry.data;
    }
    /**
     * 🚀 ULTRA-FAST: Get cache statistics
     */
    getStats() {
        const total = this.hitCount + this.missCount;
        const hitRate = total > 0 ? (this.hitCount / total) * 100 : 0;
        const totalLayerRequests = this.metrics.hotHits + this.metrics.warmHits + this.metrics.coldHits + this.metrics.totalMisses;
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys()),
            hitRate: Math.round(hitRate * 100) / 100,
            hitCount: this.hitCount,
            missCount: this.missCount,
            hotCacheSize: this.hotCache.size,
            warmCacheSize: this.warmCache.size,
            coldCacheSize: this.coldCache.size,
            promotions: this.metrics.promotions,
            evictions: this.metrics.evictions,
            layerHitRates: {
                hot: totalLayerRequests > 0 ? Math.round((this.metrics.hotHits / totalLayerRequests) * 10000) / 100 : 0,
                warm: totalLayerRequests > 0 ? Math.round((this.metrics.warmHits / totalLayerRequests) * 10000) / 100 : 0,
                cold: totalLayerRequests > 0 ? Math.round((this.metrics.coldHits / totalLayerRequests) * 10000) / 100 : 0
            }
        };
    }
    /**
     * 🚀 ULTRA-FAST: Reset statistics
     */
    resetStats() {
        this.hitCount = 0;
        this.missCount = 0;
        this.metrics = {
            hotHits: 0,
            warmHits: 0,
            coldHits: 0,
            totalMisses: 0,
            hitRate: 0,
            promotions: 0,
            evictions: 0
        };
        console.log('📊 ULTRA-FAST: All statistics reset');
    }
}
//# sourceMappingURL=CacheService.js.map