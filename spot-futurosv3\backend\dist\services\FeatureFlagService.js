import { MetricsService } from './MetricsService.js';
/**
 * 🚩 FEATURE FLAGS: Comprehensive feature flag system for gradual rollout
 * Supports percentage rollouts, conditions, and emergency disable
 */
export class FeatureFlagService {
    static instance;
    metricsService;
    config;
    evaluationCache = new Map();
    CACHE_TTL = 60000; // 1 minute cache
    // 🚩 OPTIMIZATION FLAGS: All ultra-low latency optimization features
    DEFAULT_FLAGS = {
        'ultra-parallel-processing': {
            name: 'ultra-parallel-processing',
            enabled: true,
            description: 'Enable ultra-parallel exchange data processing',
            rolloutPercentage: 100,
            conditions: {
                environment: ['production', 'staging']
            }
        },
        'multi-layer-cache': {
            name: 'multi-layer-cache',
            enabled: true,
            description: 'Enable 5-layer cache system with ML prediction',
            rolloutPercentage: 100
        },
        'batch-processing': {
            name: 'batch-processing',
            enabled: true,
            description: 'Enable DataCollector batch processing optimization',
            rolloutPercentage: 100
        },
        'websocket-streaming': {
            name: 'websocket-streaming',
            enabled: true,
            description: 'Enable real-time WebSocket streaming',
            rolloutPercentage: 100
        },
        'frontend-optimization': {
            name: 'frontend-optimization',
            enabled: true,
            description: 'Enable React.memo and virtual scrolling optimizations',
            rolloutPercentage: 100
        },
        'regional-processing': {
            name: 'regional-processing',
            enabled: true,
            description: 'Enable distributed regional processing',
            rolloutPercentage: 90, // Gradual rollout
            conditions: {
                environment: ['production']
            }
        },
        'message-streaming': {
            name: 'message-streaming',
            enabled: true,
            description: 'Enable message streaming aggregation',
            rolloutPercentage: 90
        },
        'advanced-monitoring': {
            name: 'advanced-monitoring',
            enabled: true,
            description: 'Enable advanced performance monitoring with percentiles',
            rolloutPercentage: 100
        },
        'performance-alerts': {
            name: 'performance-alerts',
            enabled: true,
            description: 'Enable real-time performance alerting',
            rolloutPercentage: 100
        },
        'auto-rollback': {
            name: 'auto-rollback',
            enabled: false, // Start disabled for safety
            description: 'Enable automatic rollback on performance degradation',
            rolloutPercentage: 0,
            conditions: {
                environment: ['staging']
            }
        }
    };
    constructor() {
        this.metricsService = MetricsService.getInstance();
        this.initializeFeatureFlags();
        this.startPerformanceMonitoring();
        console.log('🚩 FeatureFlagService initialized with optimization flags');
    }
    static getInstance() {
        if (!FeatureFlagService.instance) {
            FeatureFlagService.instance = new FeatureFlagService();
        }
        return FeatureFlagService.instance;
    }
    /**
     * 🚩 FEATURE FLAGS: Initialize feature flags from environment and defaults
     */
    initializeFeatureFlags() {
        const now = Date.now();
        // Load from environment variables
        const flags = {};
        Object.entries(this.DEFAULT_FLAGS).forEach(([flagName, defaultFlag]) => {
            const envKey = `FEATURE_${flagName.toUpperCase().replace(/-/g, '_')}`;
            const envEnabled = process.env[envKey];
            const envRollout = process.env[`${envKey}_ROLLOUT`];
            flags[flagName] = {
                ...defaultFlag,
                enabled: envEnabled !== undefined ? envEnabled === 'true' : defaultFlag.enabled,
                rolloutPercentage: envRollout ? parseInt(envRollout) : defaultFlag.rolloutPercentage,
                metadata: {
                    createdAt: now,
                    updatedAt: now,
                    createdBy: 'system',
                    version: 1
                }
            };
        });
        this.config = {
            flags,
            globalSettings: {
                enableLogging: process.env.FEATURE_FLAG_LOGGING === 'true',
                enableMetrics: process.env.FEATURE_FLAG_METRICS !== 'false', // Default true
                defaultRolloutPercentage: parseInt(process.env.FEATURE_FLAG_DEFAULT_ROLLOUT || '100'),
                emergencyDisable: process.env.FEATURE_FLAG_EMERGENCY_DISABLE === 'true'
            }
        };
        console.log(`🚩 Initialized ${Object.keys(flags).length} feature flags`);
    }
    /**
     * 🚩 FEATURE FLAGS: Evaluate if a feature flag is enabled
     */
    isEnabled(flagName, context) {
        const evaluation = this.evaluateFlag(flagName, context);
        // Record metrics
        if (this.config.globalSettings.enableMetrics) {
            this.metricsService.recordMetric(`feature_flag_${flagName}_evaluation`, evaluation.enabled ? 1 : 0, 'boolean', 'business');
        }
        // Log evaluation
        if (this.config.globalSettings.enableLogging) {
            console.log(`🚩 Feature flag '${flagName}': ${evaluation.enabled} (${evaluation.reason})`);
        }
        return evaluation.enabled;
    }
    /**
     * 🚩 FEATURE FLAGS: Detailed flag evaluation with caching
     */
    evaluateFlag(flagName, context) {
        const cacheKey = `${flagName}_${JSON.stringify(context || {})}`;
        const cached = this.evaluationCache.get(cacheKey);
        // Return cached result if still valid
        if (cached && cached.expiresAt > Date.now()) {
            return cached.evaluation;
        }
        const evaluation = this.performEvaluation(flagName, context);
        // Cache the result
        this.evaluationCache.set(cacheKey, {
            evaluation,
            expiresAt: Date.now() + this.CACHE_TTL
        });
        return evaluation;
    }
    /**
     * 🚩 FEATURE FLAGS: Perform actual flag evaluation
     */
    performEvaluation(flagName, context) {
        const now = Date.now();
        // Emergency disable check
        if (this.config.globalSettings.emergencyDisable) {
            return {
                flagName,
                enabled: false,
                reason: 'Emergency disable activated',
                evaluatedAt: now
            };
        }
        const flag = this.config.flags[flagName];
        // Flag doesn't exist
        if (!flag) {
            return {
                flagName,
                enabled: false,
                reason: 'Flag not found',
                evaluatedAt: now
            };
        }
        // Flag is disabled
        if (!flag.enabled) {
            return {
                flagName,
                enabled: false,
                reason: 'Flag disabled',
                evaluatedAt: now
            };
        }
        // Check conditions
        if (flag.conditions) {
            // Environment condition
            if (flag.conditions.environment && context?.environment) {
                if (!flag.conditions.environment.includes(context.environment)) {
                    return {
                        flagName,
                        enabled: false,
                        reason: `Environment '${context.environment}' not in allowed list`,
                        evaluatedAt: now
                    };
                }
            }
            // User agent condition
            if (flag.conditions.userAgent && context?.userAgent) {
                const matchesUserAgent = flag.conditions.userAgent.some(pattern => context.userAgent.includes(pattern));
                if (!matchesUserAgent) {
                    return {
                        flagName,
                        enabled: false,
                        reason: 'User agent not matched',
                        evaluatedAt: now
                    };
                }
            }
            // Time window condition
            if (flag.conditions.timeWindow) {
                const startTime = new Date(flag.conditions.timeWindow.start).getTime();
                const endTime = new Date(flag.conditions.timeWindow.end).getTime();
                if (now < startTime || now > endTime) {
                    return {
                        flagName,
                        enabled: false,
                        reason: 'Outside time window',
                        evaluatedAt: now
                    };
                }
            }
        }
        // Rollout percentage check
        if (flag.rolloutPercentage < 100) {
            const rolloutBucket = this.calculateRolloutBucket(flagName, context?.userId);
            if (rolloutBucket > flag.rolloutPercentage) {
                return {
                    flagName,
                    enabled: false,
                    reason: `Rollout bucket ${rolloutBucket} > ${flag.rolloutPercentage}%`,
                    rolloutBucket,
                    evaluatedAt: now
                };
            }
        }
        return {
            flagName,
            enabled: true,
            reason: 'All conditions met',
            rolloutBucket: flag.rolloutPercentage < 100 ? this.calculateRolloutBucket(flagName, context?.userId) : undefined,
            evaluatedAt: now
        };
    }
    /**
     * 🚩 FEATURE FLAGS: Calculate consistent rollout bucket for user
     */
    calculateRolloutBucket(flagName, userId) {
        // Use consistent hashing for stable rollout buckets
        const hashInput = `${flagName}_${userId || 'anonymous'}`;
        let hash = 0;
        for (let i = 0; i < hashInput.length; i++) {
            const char = hashInput.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash) % 100;
    }
    /**
     * 🚩 FEATURE FLAGS: Update feature flag configuration
     */
    updateFlag(flagName, updates) {
        const flag = this.config.flags[flagName];
        if (!flag) {
            console.error(`🚩 Cannot update non-existent flag: ${flagName}`);
            return false;
        }
        // Update flag
        this.config.flags[flagName] = {
            ...flag,
            ...updates,
            metadata: {
                ...flag.metadata,
                updatedAt: Date.now(),
                version: flag.metadata.version + 1
            }
        };
        // Clear cache for this flag
        this.clearFlagCache(flagName);
        // Record metrics
        this.metricsService.recordMetric(`feature_flag_${flagName}_updated`, 1, 'count', 'business');
        console.log(`🚩 Updated feature flag '${flagName}':`, updates);
        return true;
    }
    /**
     * 🚩 FEATURE FLAGS: Emergency disable all flags
     */
    emergencyDisable() {
        this.config.globalSettings.emergencyDisable = true;
        this.evaluationCache.clear();
        this.metricsService.recordMetric('feature_flags_emergency_disable', 1, 'count', 'error');
        console.error('🚨 EMERGENCY: All feature flags disabled!');
    }
    /**
     * 🚩 FEATURE FLAGS: Re-enable after emergency disable
     */
    emergencyRestore() {
        this.config.globalSettings.emergencyDisable = false;
        this.evaluationCache.clear();
        this.metricsService.recordMetric('feature_flags_emergency_restore', 1, 'count', 'business');
        console.log('✅ Feature flags restored from emergency disable');
    }
    /**
     * 🚩 FEATURE FLAGS: Get all feature flags status
     */
    getAllFlags() {
        return { ...this.config.flags };
    }
    /**
     * 🚩 FEATURE FLAGS: Get feature flag statistics
     */
    getStatistics() {
        const flags = Object.values(this.config.flags);
        return {
            totalFlags: flags.length,
            enabledFlags: flags.filter(f => f.enabled).length,
            flagsInRollout: flags.filter(f => f.rolloutPercentage < 100).length,
            emergencyDisabled: this.config.globalSettings.emergencyDisable,
            cacheSize: this.evaluationCache.size,
            evaluationsPerMinute: 0 // Would need to track this over time
        };
    }
    /**
     * 🚩 FEATURE FLAGS: Clear cache for specific flag
     */
    clearFlagCache(flagName) {
        const keysToDelete = [];
        this.evaluationCache.forEach((_, key) => {
            if (key.startsWith(`${flagName}_`)) {
                keysToDelete.push(key);
            }
        });
        keysToDelete.forEach(key => this.evaluationCache.delete(key));
    }
    /**
     * 🚩 FEATURE FLAGS: Start performance monitoring
     */
    startPerformanceMonitoring() {
        // Clean up expired cache entries every 5 minutes
        setInterval(() => {
            const now = Date.now();
            const keysToDelete = [];
            this.evaluationCache.forEach((value, key) => {
                if (value.expiresAt <= now) {
                    keysToDelete.push(key);
                }
            });
            keysToDelete.forEach(key => this.evaluationCache.delete(key));
            if (keysToDelete.length > 0) {
                console.log(`🚩 Cleaned up ${keysToDelete.length} expired cache entries`);
            }
        }, 300000); // 5 minutes
        // Log statistics every 10 minutes
        setInterval(() => {
            const stats = this.getStatistics();
            console.log('🚩 Feature Flag Statistics:', stats);
            // Record metrics
            this.metricsService.recordMetric('feature_flags_total', stats.totalFlags, 'count', 'business');
            this.metricsService.recordMetric('feature_flags_enabled', stats.enabledFlags, 'count', 'business');
            this.metricsService.recordMetric('feature_flags_cache_size', stats.cacheSize, 'count', 'business');
        }, 600000); // 10 minutes
    }
    /**
     * 🚩 FEATURE FLAGS: Cleanup resources
     */
    destroy() {
        this.evaluationCache.clear();
        console.log('🚩 FeatureFlagService destroyed');
    }
}
// 🚩 FEATURE FLAGS: Convenience functions for common optimization flags
export const OptimizationFlags = {
    isUltraParallelEnabled: () => FeatureFlagService.getInstance().isEnabled('ultra-parallel-processing'),
    isMultiLayerCacheEnabled: () => FeatureFlagService.getInstance().isEnabled('multi-layer-cache'),
    isBatchProcessingEnabled: () => FeatureFlagService.getInstance().isEnabled('batch-processing'),
    isWebSocketStreamingEnabled: () => FeatureFlagService.getInstance().isEnabled('websocket-streaming'),
    isFrontendOptimizationEnabled: () => FeatureFlagService.getInstance().isEnabled('frontend-optimization'),
    isRegionalProcessingEnabled: () => FeatureFlagService.getInstance().isEnabled('regional-processing'),
    isMessageStreamingEnabled: () => FeatureFlagService.getInstance().isEnabled('message-streaming'),
    isAdvancedMonitoringEnabled: () => FeatureFlagService.getInstance().isEnabled('advanced-monitoring'),
    isPerformanceAlertsEnabled: () => FeatureFlagService.getInstance().isEnabled('performance-alerts'),
    isAutoRollbackEnabled: () => FeatureFlagService.getInstance().isEnabled('auto-rollback')
};
//# sourceMappingURL=FeatureFlagService.js.map