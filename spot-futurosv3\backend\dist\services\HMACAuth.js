import CryptoJS from 'crypto-js';
export class HMACAuth {
    /**
     * Gera assinatura HMAC para Gate.io
     */
    static generateGateioSignature(secretKey, method, path, queryString, body, timestamp) {
        const message = `${method}\n${path}\n${queryString}\n${CryptoJS.SHA512(body).toString()}\n${timestamp}`;
        return CryptoJS.HmacSHA512(message, secretKey).toString();
    }
    /**
     * Gera headers de autenticação para Gate.io
     */
    static getGateioHeaders(apiKey, secretKey, config) {
        const timestamp = Math.floor(Date.now() / 1000).toString();
        const queryString = '';
        const body = config.body || '';
        const signature = this.generateGateioSignature(secretKey, config.method, config.path, queryString, body, timestamp);
        return {
            'KEY': apiKey,
            'Timestamp': timestamp,
            'SIGN': signature,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }
    /**
     * Gera assinatura HMAC para MEXC
     */
    static generateMexcSignature(secretKey, queryString) {
        return CryptoJS.HmacSHA256(queryString, secretKey).toString();
    }
    /**
     * Gera headers de autenticação para MEXC
     */
    static getMexcHeaders(apiKey, secretKey, config) {
        const timestamp = Date.now();
        const queryString = `timestamp=${timestamp}`;
        const signature = this.generateMexcSignature(secretKey, queryString);
        return {
            'X-MEXC-APIKEY': apiKey,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }
    /**
     * Gera assinatura HMAC para Bitget
     */
    static generateBitgetSignature(secretKey, timestamp, method, requestPath, body) {
        const message = timestamp + method.toUpperCase() + requestPath + body;
        const signature = CryptoJS.HmacSHA256(message, secretKey);
        return CryptoJS.enc.Base64.stringify(signature);
    }
    /**
     * Gera headers de autenticação para Bitget
     */
    static getBitgetHeaders(apiKey, secretKey, passphrase, config) {
        const timestamp = Date.now().toString();
        const body = config.body || '';
        const signature = this.generateBitgetSignature(secretKey, timestamp, config.method, config.path, body);
        return {
            'ACCESS-KEY': apiKey,
            'ACCESS-SIGN': signature,
            'ACCESS-TIMESTAMP': timestamp,
            'ACCESS-PASSPHRASE': passphrase,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }
}
//# sourceMappingURL=HMACAuth.js.map