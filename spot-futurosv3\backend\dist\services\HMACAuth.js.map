{"version": 3, "file": "HMACAuth.js", "sourceRoot": "", "sources": ["../../src/services/HMACAuth.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,WAAW,CAAC;AAGjC,MAAM,OAAO,QAAQ;IACnB;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAC5B,SAAiB,EACjB,MAAc,EACd,IAAY,EACZ,WAAmB,EACnB,IAAY,EACZ,SAAiB;QAEjB,MAAM,OAAO,GAAG,GAAG,MAAM,KAAK,IAAI,KAAK,WAAW,KAAK,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,SAAS,EAAE,CAAC;QACxG,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,MAAc,EAAE,SAAiB,EAAE,MAAkB;QAC3E,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC3D,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAC5C,SAAS,EACT,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,IAAI,EACX,WAAW,EACX,IAAI,EACJ,SAAS,CACV,CAAC;QAEF,OAAO;YACL,KAAK,EAAE,MAAM;YACb,WAAW,EAAE,SAAS;YACtB,MAAM,EAAE,SAAS;YACjB,cAAc,EAAE,kBAAkB;YAClC,QAAQ,EAAE,kBAAkB;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,SAAiB,EAAE,WAAmB;QACjE,OAAO,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,MAAc,EAAE,SAAiB,EAAE,MAAkB;QACzE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAG,aAAa,SAAS,EAAE,CAAC;QAE7C,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAErE,OAAO;YACL,eAAe,EAAE,MAAM;YACvB,cAAc,EAAE,kBAAkB;YAClC,QAAQ,EAAE,kBAAkB;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAC5B,SAAiB,EACjB,SAAiB,EACjB,MAAc,EACd,WAAmB,EACnB,IAAY;QAEZ,MAAM,OAAO,GAAG,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,GAAG,WAAW,GAAG,IAAI,CAAC;QACtE,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC1D,OAAO,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACrB,MAAc,EACd,SAAiB,EACjB,UAAkB,EAClB,MAAkB;QAElB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QACxC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAC5C,SAAS,EACT,SAAS,EACT,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,IAAI,EACX,IAAI,CACL,CAAC;QAEF,OAAO;YACL,YAAY,EAAE,MAAM;YACpB,aAAa,EAAE,SAAS;YACxB,kBAAkB,EAAE,SAAS;YAC7B,mBAAmB,EAAE,UAAU;YAC/B,cAAc,EAAE,kBAAkB;YAClC,QAAQ,EAAE,kBAAkB;SAC7B,CAAC;IACJ,CAAC;CACF"}