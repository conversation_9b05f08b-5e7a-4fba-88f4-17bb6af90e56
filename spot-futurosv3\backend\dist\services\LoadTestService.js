// LoadTestService - Sistema de testes de stress e load testing
// Implementa testes de carga progressiva, spike testing, endurance testing
import axios from 'axios';
export class LoadTestService {
    static instance;
    activeTests = new Map();
    testResults = [];
    // 🚀 TEST CONFIGURATIONS: Predefined test scenarios
    TEST_SCENARIOS = {
        LIGHT_LOAD: {
            maxRPS: 50,
            duration: 300, // 5 minutes
            rampUpTime: 60, // 1 minute
            testType: 'LOAD'
        },
        NORMAL_LOAD: {
            maxRPS: 100,
            duration: 600, // 10 minutes
            rampUpTime: 120, // 2 minutes
            testType: 'LOAD'
        },
        STRESS_TEST: {
            maxRPS: 250,
            duration: 300, // 5 minutes
            rampUpTime: 60, // 1 minute
            testType: 'STRESS'
        },
        SPIKE_TEST: {
            maxRPS: 500,
            duration: 120, // 2 minutes
            rampUpTime: 10, // 10 seconds
            testType: 'SPIKE'
        },
        ENDURANCE_TEST: {
            maxRPS: 75,
            duration: 3600, // 1 hour
            rampUpTime: 300, // 5 minutes
            testType: 'ENDURANCE'
        }
    };
    static getInstance() {
        if (!LoadTestService.instance) {
            LoadTestService.instance = new LoadTestService();
        }
        return LoadTestService.instance;
    }
    /**
     * 🚀 LOAD TEST: Start load test with configuration
     */
    async startLoadTest(config) {
        const testId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        console.log(`🚀 LoadTest: Starting ${config.testType} test ${testId}`);
        console.log(`📊 Config: ${config.maxRPS} RPS for ${config.duration}s`);
        const result = {
            testId,
            config,
            startTime: Date.now(),
            endTime: 0,
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageLatency: 0,
            p90Latency: 0,
            p95Latency: 0,
            p99Latency: 0,
            maxLatency: 0,
            minLatency: Infinity,
            actualRPS: 0,
            errorRate: 0,
            errors: {},
            latencyHistory: [],
            rpsHistory: [],
            memoryUsage: []
        };
        // Start the test
        const stopTest = this.executeLoadTest(config, result);
        this.activeTests.set(testId, {
            config,
            startTime: Date.now(),
            stop: stopTest
        });
        return testId;
    }
    /**
     * 🚀 EXECUTION: Execute load test
     */
    executeLoadTest(config, result) {
        let isRunning = true;
        let currentRPS = 0;
        const startTime = Date.now();
        // RPS tracking
        const rpsTracker = setInterval(() => {
            if (!isRunning)
                return;
            const elapsed = (Date.now() - startTime) / 1000;
            const targetRPS = this.calculateTargetRPS(config, elapsed);
            result.rpsHistory.push({
                timestamp: Date.now(),
                rps: currentRPS
            });
            currentRPS = 0; // Reset for next interval
        }, 1000);
        // Memory tracking
        const memoryTracker = setInterval(() => {
            if (!isRunning)
                return;
            const memUsage = process.memoryUsage();
            result.memoryUsage.push({
                timestamp: Date.now(),
                usage: (memUsage.heapUsed / 1024 / 1024) // MB
            });
        }, 5000);
        // Main test loop
        const testLoop = setInterval(async () => {
            if (!isRunning)
                return;
            const elapsed = (Date.now() - startTime) / 1000;
            // Check if test should end
            if (elapsed >= config.duration) {
                this.stopLoadTest(result.testId);
                return;
            }
            const targetRPS = this.calculateTargetRPS(config, elapsed);
            const requestsThisSecond = Math.floor(targetRPS);
            // Execute requests for this second
            for (let i = 0; i < requestsThisSecond; i++) {
                if (!isRunning)
                    break;
                // Spread requests across the second
                const delay = (1000 / requestsThisSecond) * i;
                setTimeout(async () => {
                    if (!isRunning)
                        return;
                    try {
                        await this.executeRequest(config, result);
                        currentRPS++;
                    }
                    catch (error) {
                        // Error already handled in executeRequest
                    }
                }, delay);
            }
        }, 1000);
        // Stop function
        return () => {
            isRunning = false;
            clearInterval(rpsTracker);
            clearInterval(memoryTracker);
            clearInterval(testLoop);
            // Finalize results
            result.endTime = Date.now();
            result.actualRPS = result.totalRequests / ((result.endTime - result.startTime) / 1000);
            result.errorRate = (result.failedRequests / result.totalRequests) * 100;
            result.averageLatency = result.latencyHistory.reduce((sum, lat) => sum + lat, 0) / result.latencyHistory.length;
            // Calculate percentiles
            const sortedLatencies = [...result.latencyHistory].sort((a, b) => a - b);
            const len = sortedLatencies.length;
            if (len > 0) {
                result.p90Latency = sortedLatencies[Math.floor(len * 0.9)] || 0;
                result.p95Latency = sortedLatencies[Math.floor(len * 0.95)] || 0;
                result.p99Latency = sortedLatencies[Math.floor(len * 0.99)] || 0;
                result.maxLatency = Math.max(...sortedLatencies);
                result.minLatency = Math.min(...sortedLatencies);
            }
            this.testResults.push(result);
            this.activeTests.delete(result.testId);
            console.log(`✅ LoadTest: Test ${result.testId} completed`);
            console.log(`📊 Results: ${result.totalRequests} requests, ${result.actualRPS.toFixed(1)} RPS, P95: ${result.p95Latency}ms`);
        };
    }
    /**
     * 🚀 CALCULATION: Calculate target RPS based on test type and elapsed time
     */
    calculateTargetRPS(config, elapsedSeconds) {
        const { maxRPS, rampUpTime, testType } = config;
        if (elapsedSeconds <= rampUpTime) {
            // Ramp up phase
            return (elapsedSeconds / rampUpTime) * maxRPS;
        }
        switch (testType) {
            case 'SPIKE':
                // Immediate spike to max RPS
                return maxRPS;
            case 'STRESS':
                // Gradual increase beyond normal capacity
                const stressMultiplier = 1 + (elapsedSeconds - rampUpTime) / 300; // Increase over 5 minutes
                return Math.min(maxRPS * stressMultiplier, maxRPS * 2);
            case 'ENDURANCE':
                // Steady load for extended period
                return maxRPS * 0.8; // 80% of max for sustainability
            default: // LOAD
                return maxRPS;
        }
    }
    /**
     * 🚀 REQUEST: Execute single request
     */
    async executeRequest(config, result) {
        const endpoint = config.endpoints[Math.floor(Math.random() * config.endpoints.length)];
        const url = `${config.baseURL}${endpoint}`;
        const startTime = Date.now();
        try {
            const response = await axios.get(url, {
                timeout: 10000, // 10s timeout
                validateStatus: (status) => status < 500 // Accept 4xx as success
            });
            const latency = Date.now() - startTime;
            result.latencyHistory.push(latency);
            result.totalRequests++;
            result.successfulRequests++;
        }
        catch (error) {
            const latency = Date.now() - startTime;
            result.latencyHistory.push(latency);
            result.totalRequests++;
            result.failedRequests++;
            // Track error types
            const errorType = error instanceof Error ? error.message : 'Unknown error';
            result.errors[errorType] = (result.errors[errorType] || 0) + 1;
        }
    }
    /**
     * 🚀 CONTROL: Stop load test
     */
    stopLoadTest(testId) {
        const test = this.activeTests.get(testId);
        if (test) {
            test.stop();
            console.log(`🛑 LoadTest: Stopped test ${testId}`);
            return true;
        }
        return false;
    }
    /**
     * 🚀 SCENARIOS: Start predefined test scenario
     */
    async startTestScenario(scenarioName, baseURL, endpoints) {
        const scenario = this.TEST_SCENARIOS[scenarioName];
        const config = {
            baseURL,
            endpoints,
            ...scenario
        };
        return this.startLoadTest(config);
    }
    /**
     * 🚀 API: Get active tests
     */
    getActiveTests() {
        const now = Date.now();
        return Array.from(this.activeTests.entries()).map(([testId, test]) => ({
            testId,
            config: test.config,
            startTime: test.startTime,
            elapsed: Math.floor((now - test.startTime) / 1000)
        }));
    }
    /**
     * 🚀 API: Get test results
     */
    getTestResults(limit = 10) {
        return this.testResults
            .sort((a, b) => b.startTime - a.startTime)
            .slice(0, limit);
    }
    /**
     * 🚀 API: Get test result by ID
     */
    getTestResult(testId) {
        return this.testResults.find(result => result.testId === testId) || null;
    }
    /**
     * 🚀 VALIDATION: Validate test results against targets
     */
    validateTestResults(testId) {
        const result = this.getTestResult(testId);
        if (!result) {
            return {
                passed: false,
                results: {
                    p90LatencyTarget: false,
                    p99LatencyTarget: false,
                    errorRateTarget: false,
                    rpsTarget: false
                },
                summary: 'Test result not found'
            };
        }
        const results = {
            p90LatencyTarget: result.p90Latency < 800,
            p99LatencyTarget: result.p99Latency < 1200,
            errorRateTarget: result.errorRate < 1,
            rpsTarget: result.actualRPS >= result.config.maxRPS * 0.9 // 90% of target
        };
        const passed = Object.values(results).every(Boolean);
        const summary = `P90: ${result.p90Latency}ms (${results.p90LatencyTarget ? '✅' : '❌'}), ` +
            `P99: ${result.p99Latency}ms (${results.p99LatencyTarget ? '✅' : '❌'}), ` +
            `Error Rate: ${result.errorRate.toFixed(2)}% (${results.errorRateTarget ? '✅' : '❌'}), ` +
            `RPS: ${result.actualRPS.toFixed(1)} (${results.rpsTarget ? '✅' : '❌'})`;
        return { passed, results, summary };
    }
}
//# sourceMappingURL=LoadTestService.js.map