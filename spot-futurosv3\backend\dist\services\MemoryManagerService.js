// MemoryManagerService - Gestão inteligente de memória e garbage collection
// Implementa pooling de objetos, cleanup automático e otimização de GC
export class MemoryManagerService {
    static instance;
    objectPools = new Map();
    memoryHistory = [];
    gcStats = {
        forcedCollections: 0,
        lastCollection: 0,
        averageCollectionTime: 0
    };
    // 🚀 MEMORY LIMITS: Thresholds for memory management
    MEMORY_THRESHOLDS = {
        WARNING: 80, // 80% memory usage warning
        CRITICAL: 90, // 90% memory usage critical
        FORCE_GC: 85, // Force GC at 85%
        CLEANUP: 75 // Start cleanup at 75%
    };
    // 🚀 POOL SIZES: Object pool configurations
    POOL_CONFIGS = {
        OPPORTUNITY_OBJECTS: { maxSize: 1000, initialSize: 100 },
        CACHE_ENTRIES: { maxSize: 500, initialSize: 50 },
        WEBSOCKET_MESSAGES: { maxSize: 200, initialSize: 20 },
        METRICS_OBJECTS: { maxSize: 100, initialSize: 10 }
    };
    static getInstance() {
        if (!MemoryManagerService.instance) {
            MemoryManagerService.instance = new MemoryManagerService();
            MemoryManagerService.instance.initialize();
        }
        return MemoryManagerService.instance;
    }
    /**
     * 🚀 INITIALIZATION: Setup memory management
     */
    initialize() {
        // Initialize object pools
        this.initializeObjectPools();
        // Start memory monitoring
        this.startMemoryMonitoring();
        // Setup garbage collection optimization
        this.setupGCOptimization();
        console.log('🧠 MemoryManager: Initialized with intelligent memory management');
    }
    /**
     * 🚀 POOLS: Initialize object pools for common objects
     */
    initializeObjectPools() {
        // Opportunity objects pool
        this.createObjectPool('opportunities', () => ({
            id: '',
            symbol: '',
            spotExchange: '',
            futuresExchange: '',
            spotPrice: 0,
            futuresPrice: 0,
            spreadPercentage: 0,
            spotVolume: 0,
            futuresVolume: 0,
            profitability: 'LOW',
            type: 'spot_futures',
            lastUpdate: new Date(),
            confidence: 0,
            estimatedProfit: 0,
            riskLevel: 'LOW'
        }), (obj) => {
            obj.id = '';
            obj.symbol = '';
            obj.spotExchange = '';
            obj.futuresExchange = '';
            obj.spotPrice = 0;
            obj.futuresPrice = 0;
            obj.spreadPercentage = 0;
            obj.spotVolume = 0;
            obj.futuresVolume = 0;
            obj.profitability = 'LOW';
            obj.type = 'spot_futures';
            obj.lastUpdate = new Date();
            obj.confidence = 0;
            obj.estimatedProfit = 0;
            obj.riskLevel = 'LOW';
        }, this.POOL_CONFIGS.OPPORTUNITY_OBJECTS.maxSize);
        // Cache entries pool
        this.createObjectPool('cacheEntries', () => ({
            data: null,
            timestamp: 0,
            ttl: 0,
            expiresAt: 0
        }), (obj) => {
            obj.data = null;
            obj.timestamp = 0;
            obj.ttl = 0;
            obj.expiresAt = 0;
        }, this.POOL_CONFIGS.CACHE_ENTRIES.maxSize);
        // WebSocket messages pool
        this.createObjectPool('wsMessages', () => ({
            type: '',
            data: null,
            timestamp: 0,
            id: ''
        }), (obj) => {
            obj.type = '';
            obj.data = null;
            obj.timestamp = 0;
            obj.id = '';
        }, this.POOL_CONFIGS.WEBSOCKET_MESSAGES.maxSize);
        console.log('🏊 MemoryManager: Object pools initialized');
    }
    /**
     * 🚀 POOL: Create object pool
     */
    createObjectPool(name, createFn, resetFn, maxSize) {
        const pool = {
            objects: [],
            createFn,
            resetFn,
            maxSize,
            currentSize: 0
        };
        // Pre-populate pool with initial objects
        const initialSize = Math.min(maxSize / 10, 10);
        for (let i = 0; i < initialSize; i++) {
            pool.objects.push(createFn());
            pool.currentSize++;
        }
        this.objectPools.set(name, pool);
    }
    /**
     * 🚀 POOL: Get object from pool
     */
    getFromPool(poolName) {
        const pool = this.objectPools.get(poolName);
        if (!pool)
            return null;
        if (pool.objects.length > 0) {
            const obj = pool.objects.pop();
            pool.resetFn(obj);
            return obj;
        }
        // Pool is empty, create new object if under limit
        if (pool.currentSize < pool.maxSize) {
            pool.currentSize++;
            return pool.createFn();
        }
        // Pool is full, return null (caller should create manually)
        return null;
    }
    /**
     * 🚀 POOL: Return object to pool
     */
    returnToPool(poolName, obj) {
        const pool = this.objectPools.get(poolName);
        if (!pool)
            return;
        if (pool.objects.length < pool.maxSize) {
            pool.resetFn(obj);
            pool.objects.push(obj);
        }
        else {
            // Pool is full, let object be garbage collected
            pool.currentSize--;
        }
    }
    /**
     * 🚀 MONITORING: Start continuous memory monitoring
     */
    startMemoryMonitoring() {
        setInterval(() => {
            this.checkMemoryUsage();
        }, 10000); // Check every 10 seconds
        setInterval(() => {
            this.cleanupMemoryHistory();
        }, 300000); // Cleanup every 5 minutes
        console.log('📊 MemoryManager: Memory monitoring started');
    }
    /**
     * 🚀 MONITORING: Check current memory usage
     */
    checkMemoryUsage() {
        const memUsage = process.memoryUsage();
        const stats = {
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            external: memUsage.external,
            arrayBuffers: memUsage.arrayBuffers,
            rss: memUsage.rss,
            percentage: (memUsage.heapUsed / memUsage.heapTotal) * 100
        };
        this.memoryHistory.push(stats);
        // Check thresholds and take action
        if (stats.percentage > this.MEMORY_THRESHOLDS.CRITICAL) {
            console.error(`🚨 CRITICAL: Memory usage at ${stats.percentage.toFixed(1)}%`);
            this.emergencyCleanup();
            this.forceGarbageCollection();
        }
        else if (stats.percentage > this.MEMORY_THRESHOLDS.FORCE_GC) {
            console.warn(`⚠️ HIGH: Memory usage at ${stats.percentage.toFixed(1)}%, forcing GC`);
            this.forceGarbageCollection();
        }
        else if (stats.percentage > this.MEMORY_THRESHOLDS.CLEANUP) {
            console.log(`🧹 CLEANUP: Memory usage at ${stats.percentage.toFixed(1)}%, starting cleanup`);
            this.performCleanup();
        }
    }
    /**
     * 🚀 GC: Setup garbage collection optimization
     */
    setupGCOptimization() {
        // Optimize GC for server workloads
        if (global.gc) {
            console.log('🗑️ MemoryManager: Manual GC available');
        }
        else {
            console.warn('⚠️ MemoryManager: Manual GC not available (run with --expose-gc)');
        }
        // Monitor GC events if available
        if (process.env.NODE_ENV === 'development') {
            process.on('warning', (warning) => {
                if (warning.name === 'MaxListenersExceededWarning') {
                    console.warn('🔧 MemoryManager: MaxListeners warning detected');
                }
            });
        }
    }
    /**
     * 🚀 GC: Force garbage collection
     */
    forceGarbageCollection() {
        if (global.gc) {
            const startTime = Date.now();
            global.gc();
            const collectionTime = Date.now() - startTime;
            this.gcStats.forcedCollections++;
            this.gcStats.lastCollection = Date.now();
            this.gcStats.averageCollectionTime =
                (this.gcStats.averageCollectionTime + collectionTime) / 2;
            console.log(`🗑️ MemoryManager: Forced GC completed in ${collectionTime}ms`);
        }
    }
    /**
     * 🚀 CLEANUP: Perform memory cleanup
     */
    performCleanup() {
        // Cleanup object pools
        this.cleanupObjectPools();
        // Cleanup memory history
        this.cleanupMemoryHistory();
        console.log('🧹 MemoryManager: Cleanup completed');
    }
    /**
     * 🚀 CLEANUP: Emergency cleanup for critical memory situations
     */
    emergencyCleanup() {
        console.log('🚨 MemoryManager: Emergency cleanup initiated');
        // Aggressively cleanup object pools
        this.objectPools.forEach((pool, name) => {
            const originalSize = pool.objects.length;
            pool.objects = pool.objects.slice(0, Math.floor(pool.maxSize / 4)); // Keep only 25%
            pool.currentSize = pool.objects.length;
            console.log(`🚨 Emergency: Reduced ${name} pool from ${originalSize} to ${pool.objects.length}`);
        });
        // Clear most of memory history
        this.memoryHistory = this.memoryHistory.slice(-10); // Keep only last 10 entries
        // Force multiple GC cycles
        if (global.gc) {
            for (let i = 0; i < 3; i++) {
                global.gc();
            }
        }
        console.log('🚨 MemoryManager: Emergency cleanup completed');
    }
    /**
     * 🚀 CLEANUP: Cleanup object pools
     */
    cleanupObjectPools() {
        this.objectPools.forEach((pool, name) => {
            const targetSize = Math.floor(pool.maxSize * 0.7); // Keep 70% of max size
            if (pool.objects.length > targetSize) {
                const removed = pool.objects.length - targetSize;
                pool.objects = pool.objects.slice(0, targetSize);
                pool.currentSize = pool.objects.length;
                console.log(`🧹 Cleaned ${name} pool: removed ${removed} objects`);
            }
        });
    }
    /**
     * 🚀 CLEANUP: Cleanup memory history
     */
    cleanupMemoryHistory() {
        // Keep only last hour of memory history
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        const originalLength = this.memoryHistory.length;
        this.memoryHistory = this.memoryHistory.filter((_, index) => index >= originalLength - 360 // Keep last 360 entries (1 hour at 10s intervals)
        );
    }
    /**
     * 🚀 API: Get current memory statistics
     */
    getMemoryStats() {
        const current = this.memoryHistory[this.memoryHistory.length - 1] || {
            heapUsed: 0, heapTotal: 0, external: 0, arrayBuffers: 0, rss: 0, percentage: 0
        };
        const pools = {};
        this.objectPools.forEach((pool, name) => {
            pools[name] = {
                size: pool.objects.length,
                maxSize: pool.maxSize,
                utilization: (pool.objects.length / pool.maxSize) * 100
            };
        });
        return {
            current,
            pools,
            gc: { ...this.gcStats },
            history: this.memoryHistory.slice(-60) // Last 10 minutes
        };
    }
    /**
     * 🚀 API: Get memory health status
     */
    getMemoryHealth() {
        const current = this.memoryHistory[this.memoryHistory.length - 1];
        if (!current) {
            return { status: 'HEALTHY', percentage: 0, recommendation: 'No data available' };
        }
        let status = 'HEALTHY';
        let recommendation = 'Memory usage is optimal';
        if (current.percentage > this.MEMORY_THRESHOLDS.CRITICAL) {
            status = 'CRITICAL';
            recommendation = 'Critical memory usage! Consider restarting the service or reducing load';
        }
        else if (current.percentage > this.MEMORY_THRESHOLDS.WARNING) {
            status = 'WARNING';
            recommendation = 'High memory usage. Monitor closely and consider cleanup';
        }
        return {
            status,
            percentage: current.percentage,
            recommendation
        };
    }
}
//# sourceMappingURL=MemoryManagerService.js.map