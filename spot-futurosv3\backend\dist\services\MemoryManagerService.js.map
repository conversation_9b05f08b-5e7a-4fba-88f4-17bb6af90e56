{"version": 3, "file": "MemoryManagerService.js", "sourceRoot": "", "sources": ["../../src/services/MemoryManagerService.ts"], "names": [], "mappings": "AAAA,4EAA4E;AAC5E,uEAAuE;AAmBvE,MAAM,OAAO,oBAAoB;IACvB,MAAM,CAAC,QAAQ,CAAsB;IACrC,WAAW,GAAG,IAAI,GAAG,EAA2B,CAAA;IAChD,aAAa,GAAkB,EAAE,CAAA;IACjC,OAAO,GAAG;QAChB,iBAAiB,EAAE,CAAC;QACpB,cAAc,EAAE,CAAC;QACjB,qBAAqB,EAAE,CAAC;KACzB,CAAA;IAED,qDAAqD;IACpC,iBAAiB,GAAG;QACnC,OAAO,EAAE,EAAE,EAAE,2BAA2B;QACxC,QAAQ,EAAE,EAAE,EAAE,4BAA4B;QAC1C,QAAQ,EAAE,EAAE,EAAE,kBAAkB;QAChC,OAAO,EAAE,EAAE,CAAC,uBAAuB;KACpC,CAAA;IAED,4CAA4C;IAC3B,YAAY,GAAG;QAC9B,mBAAmB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE;QACxD,aAAa,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,EAAE,EAAE;QAChD,kBAAkB,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,EAAE,EAAE;QACrD,eAAe,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,EAAE,EAAE;KACnD,CAAA;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;YACnC,oBAAoB,CAAC,QAAQ,GAAG,IAAI,oBAAoB,EAAE,CAAA;YAC1D,oBAAoB,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAA;QAC5C,CAAC;QACD,OAAO,oBAAoB,CAAC,QAAQ,CAAA;IACtC,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,0BAA0B;QAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAE5B,0BAA0B;QAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAE5B,wCAAwC;QACxC,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAE1B,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAA;IACjF,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,2BAA2B;QAC3B,IAAI,CAAC,gBAAgB,CAAC,eAAe,EACnC,GAAG,EAAE,CAAC,CAAC;YACL,EAAE,EAAE,EAAE;YACN,MAAM,EAAE,EAAE;YACV,YAAY,EAAE,EAAE;YAChB,eAAe,EAAE,EAAE;YACnB,SAAS,EAAE,CAAC;YACZ,YAAY,EAAE,CAAC;YACf,gBAAgB,EAAE,CAAC;YACnB,UAAU,EAAE,CAAC;YACb,aAAa,EAAE,CAAC;YAChB,aAAa,EAAE,KAAK;YACpB,IAAI,EAAE,cAAc;YACpB,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,UAAU,EAAE,CAAC;YACb,eAAe,EAAE,CAAC;YAClB,SAAS,EAAE,KAAK;SACjB,CAAC,EACF,CAAC,GAAG,EAAE,EAAE;YACN,GAAG,CAAC,EAAE,GAAG,EAAE,CAAA;YACX,GAAG,CAAC,MAAM,GAAG,EAAE,CAAA;YACf,GAAG,CAAC,YAAY,GAAG,EAAE,CAAA;YACrB,GAAG,CAAC,eAAe,GAAG,EAAE,CAAA;YACxB,GAAG,CAAC,SAAS,GAAG,CAAC,CAAA;YACjB,GAAG,CAAC,YAAY,GAAG,CAAC,CAAA;YACpB,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAA;YACxB,GAAG,CAAC,UAAU,GAAG,CAAC,CAAA;YAClB,GAAG,CAAC,aAAa,GAAG,CAAC,CAAA;YACrB,GAAG,CAAC,aAAa,GAAG,KAAK,CAAA;YACzB,GAAG,CAAC,IAAI,GAAG,cAAc,CAAA;YACzB,GAAG,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAA;YAC3B,GAAG,CAAC,UAAU,GAAG,CAAC,CAAA;YAClB,GAAG,CAAC,eAAe,GAAG,CAAC,CAAA;YACvB,GAAG,CAAC,SAAS,GAAG,KAAK,CAAA;QACvB,CAAC,EACD,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAC9C,CAAA;QAED,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAClC,GAAG,EAAE,CAAC,CAAC;YACL,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,CAAC;YACZ,GAAG,EAAE,CAAC;YACN,SAAS,EAAE,CAAC;SACb,CAAC,EACF,CAAC,GAAG,EAAE,EAAE;YACN,GAAG,CAAC,IAAI,GAAG,IAAI,CAAA;YACf,GAAG,CAAC,SAAS,GAAG,CAAC,CAAA;YACjB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAA;YACX,GAAG,CAAC,SAAS,GAAG,CAAC,CAAA;QACnB,CAAC,EACD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,CACxC,CAAA;QAED,0BAA0B;QAC1B,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAChC,GAAG,EAAE,CAAC,CAAC;YACL,IAAI,EAAE,EAAE;YACR,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,CAAC;YACZ,EAAE,EAAE,EAAE;SACP,CAAC,EACF,CAAC,GAAG,EAAE,EAAE;YACN,GAAG,CAAC,IAAI,GAAG,EAAE,CAAA;YACb,GAAG,CAAC,IAAI,GAAG,IAAI,CAAA;YACf,GAAG,CAAC,SAAS,GAAG,CAAC,CAAA;YACjB,GAAG,CAAC,EAAE,GAAG,EAAE,CAAA;QACb,CAAC,EACD,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,OAAO,CAC7C,CAAA;QAED,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACK,gBAAgB,CACtB,IAAY,EACZ,QAAiB,EACjB,OAAyB,EACzB,OAAe;QAEf,MAAM,IAAI,GAAkB;YAC1B,OAAO,EAAE,EAAE;YACX,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW,EAAE,CAAC;SACf,CAAA;QAED,yCAAyC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,EAAE,EAAE,EAAE,CAAC,CAAA;QAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;YAC7B,IAAI,CAAC,WAAW,EAAE,CAAA;QACpB,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAClC,CAAC;IAED;;OAEG;IACH,WAAW,CAAI,QAAgB;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAkB,CAAA;QAC5D,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAA;QAEtB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAG,CAAA;YAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;YACjB,OAAO,GAAG,CAAA;QACZ,CAAC;QAED,kDAAkD;QAClD,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,WAAW,EAAE,CAAA;YAClB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAA;QACxB,CAAC;QAED,4DAA4D;QAC5D,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,YAAY,CAAI,QAAgB,EAAE,GAAM;QACtC,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAkB,CAAA;QAC5D,IAAI,CAAC,IAAI;YAAE,OAAM;QAEjB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACxB,CAAC;aAAM,CAAC;YACN,gDAAgD;YAChD,IAAI,CAAC,WAAW,EAAE,CAAA;QACpB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACzB,CAAC,EAAE,KAAK,CAAC,CAAA,CAAC,yBAAyB;QAEnC,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAC7B,CAAC,EAAE,MAAM,CAAC,CAAA,CAAC,0BAA0B;QAErC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAA;IAC5D,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAA;QACtC,MAAM,KAAK,GAAgB;YACzB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;YAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,YAAY,EAAE,QAAQ,CAAC,YAAY;YACnC,GAAG,EAAE,QAAQ,CAAC,GAAG;YACjB,UAAU,EAAE,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG;SAC3D,CAAA;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAE9B,mCAAmC;QACnC,IAAI,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YACvD,OAAO,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;YAC7E,IAAI,CAAC,gBAAgB,EAAE,CAAA;YACvB,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC/B,CAAC;aAAM,IAAI,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YAC9D,OAAO,CAAC,IAAI,CAAC,4BAA4B,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAA;YACpF,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC/B,CAAC;aAAM,IAAI,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,+BAA+B,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAA;YAC5F,IAAI,CAAC,cAAc,EAAE,CAAA;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,mCAAmC;QACnC,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAA;QACvD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAA;QAClF,CAAC;QAED,iCAAiC;QACjC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE;gBAChC,IAAI,OAAO,CAAC,IAAI,KAAK,6BAA6B,EAAE,CAAC;oBACnD,OAAO,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAA;gBACjE,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC5B,MAAM,CAAC,EAAE,EAAE,CAAA;YACX,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YAE7C,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAA;YAChC,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACxC,IAAI,CAAC,OAAO,CAAC,qBAAqB;gBAChC,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,GAAG,cAAc,CAAC,GAAG,CAAC,CAAA;YAE3D,OAAO,CAAC,GAAG,CAAC,6CAA6C,cAAc,IAAI,CAAC,CAAA;QAC9E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,uBAAuB;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEzB,yBAAyB;QACzB,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAE3B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAA;IACpD,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAA;QAE5D,oCAAoC;QACpC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACtC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;YACxC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAA,CAAC,gBAAgB;YACnF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;YACtC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,cAAc,YAAY,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;QAClG,CAAC,CAAC,CAAA;QAEF,+BAA+B;QAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAA,CAAC,4BAA4B;QAE/E,2BAA2B;QAC3B,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,CAAC,EAAE,EAAE,CAAA;YACb,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAA;IAC9D,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACtC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAA,CAAC,uBAAuB;YACzE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;gBACrC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,UAAU,CAAA;gBAChD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;gBAChD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;gBACtC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,kBAAkB,OAAO,UAAU,CAAC,CAAA;YACpE,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,wCAAwC;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;QAChD,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAA;QAChD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAC1D,KAAK,IAAI,cAAc,GAAG,GAAG,CAAC,kDAAkD;SACjF,CAAA;IACH,CAAC;IAED;;OAEG;IACH,cAAc;QAMZ,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI;YACnE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC;SAC/E,CAAA;QAED,MAAM,KAAK,GAA2E,EAAE,CAAA;QACxF,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACtC,KAAK,CAAC,IAAI,CAAC,GAAG;gBACZ,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,WAAW,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;aACxD,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,OAAO;YACL,OAAO;YACP,KAAK;YACL,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE;YACvB,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB;SAC1D,CAAA;IACH,CAAC;IAED;;OAEG;IACH,eAAe;QAKb,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QACjE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,cAAc,EAAE,mBAAmB,EAAE,CAAA;QAClF,CAAC;QAED,IAAI,MAAM,GAAuC,SAAS,CAAA;QAC1D,IAAI,cAAc,GAAG,yBAAyB,CAAA;QAE9C,IAAI,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YACzD,MAAM,GAAG,UAAU,CAAA;YACnB,cAAc,GAAG,yEAAyE,CAAA;QAC5F,CAAC;aAAM,IAAI,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC/D,MAAM,GAAG,SAAS,CAAA;YAClB,cAAc,GAAG,yDAAyD,CAAA;QAC5E,CAAC;QAED,OAAO;YACL,MAAM;YACN,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,cAAc;SACf,CAAA;IACH,CAAC;CACF"}