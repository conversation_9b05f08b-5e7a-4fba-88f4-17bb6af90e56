import { EventEmitter } from 'events';
import { MetricsService } from './MetricsService.js';
/**
 * 🌍 MESSAGE STREAMING: Ultra-fast message queue for regional result aggregation
 * Target: <100ms combination time with conflict resolution
 */
export class MessageStreamService extends EventEmitter {
    static instance;
    metricsService;
    // Message queue for regional results
    messageQueue = new Map();
    aggregationResults = new Map();
    // Performance tracking
    aggregationStartTimes = new Map();
    expectedRegions = new Map();
    // Deduplication and conflict resolution
    seenDataHashes = new Set();
    conflictResolutionRules = new Map();
    constructor() {
        super();
        this.metricsService = MetricsService.getInstance();
        this.setupConflictResolutionRules();
        this.startPerformanceMonitoring();
        console.log('🌍 MessageStreamService initialized for regional aggregation');
    }
    static getInstance() {
        if (!MessageStreamService.instance) {
            MessageStreamService.instance = new MessageStreamService();
        }
        return MessageStreamService.instance;
    }
    /**
     * 🌍 STREAMING: Start aggregation session for regional processing
     */
    startAggregationSession(sessionId, expectedRegions) {
        console.log(`🚀 Starting aggregation session ${sessionId} for regions: ${expectedRegions.join(', ')}`);
        this.messageQueue.set(sessionId, []);
        this.expectedRegions.set(sessionId, expectedRegions);
        this.aggregationStartTimes.set(sessionId, performance.now());
        // Set timeout for session completion
        setTimeout(() => {
            this.forceCompleteSession(sessionId);
        }, 5000); // 5 second timeout
        this.metricsService.recordMetric('aggregation_sessions_started', 1, 'count', 'counter');
    }
    /**
     * 🌍 STREAMING: Add regional message to aggregation queue
     */
    addRegionalMessage(sessionId, message) {
        const startTime = performance.now();
        console.log(`📨 Received message from ${message.region} region for session ${sessionId}`);
        const messages = this.messageQueue.get(sessionId);
        if (!messages) {
            console.warn(`⚠️ No active session found for ${sessionId}`);
            return;
        }
        // Add message to queue
        messages.push(message);
        // Check if we have all expected regions
        const expectedRegions = this.expectedRegions.get(sessionId) || [];
        const receivedRegions = messages.map(m => m.region);
        const allRegionsReceived = expectedRegions.every(region => receivedRegions.includes(region));
        const processingTime = performance.now() - startTime;
        this.metricsService.recordMetric('message_processing_time', processingTime, 'ms', 'latency');
        console.log(`📊 Session ${sessionId}: ${receivedRegions.length}/${expectedRegions.length} regions received`);
        if (allRegionsReceived) {
            console.log(`✅ All regions received for session ${sessionId}, starting aggregation...`);
            this.aggregateResults(sessionId);
        }
    }
    /**
     * 🌍 STREAMING: Aggregate results from all regions with conflict resolution
     */
    async aggregateResults(sessionId) {
        const aggregationStart = performance.now();
        const messages = this.messageQueue.get(sessionId) || [];
        const sessionStartTime = this.aggregationStartTimes.get(sessionId) || aggregationStart;
        console.log(`🔄 Aggregating results for session ${sessionId} with ${messages.length} messages...`);
        try {
            const allSpotData = [];
            const allFuturesData = [];
            let totalLatency = 0;
            let conflicts = 0;
            let duplicatesRemoved = 0;
            // Process messages and aggregate data
            for (const message of messages) {
                totalLatency += message.metrics.latency;
                // Add spot data with deduplication
                const spotResult = this.addDataWithDeduplication(allSpotData, message.data.spot);
                allSpotData.push(...spotResult.newData);
                duplicatesRemoved += spotResult.duplicatesRemoved;
                conflicts += spotResult.conflicts;
                // Add futures data with deduplication
                const futuresResult = this.addDataWithDeduplication(allFuturesData, message.data.futures);
                allFuturesData.push(...futuresResult.newData);
                duplicatesRemoved += futuresResult.duplicatesRemoved;
                conflicts += futuresResult.conflicts;
            }
            const aggregationTime = performance.now() - aggregationStart;
            const totalSessionTime = performance.now() - sessionStartTime;
            // 🚨 AGGREGATION ALERT: Target <100ms
            if (aggregationTime > 100) {
                console.warn(`🚨 AGGREGATION SLOW: ${aggregationTime.toFixed(1)}ms (target: <100ms)`);
                this.metricsService.recordMetric('aggregation_slow', 1, 'count', 'counter');
            }
            else {
                console.log(`⚡ AGGREGATION SUCCESS: ${aggregationTime.toFixed(1)}ms (target: <100ms) ✅`);
            }
            const result = {
                id: sessionId,
                timestamp: Date.now(),
                allSpotData,
                allFuturesData,
                metadata: {
                    totalRegions: messages.length,
                    successfulRegions: messages.length,
                    aggregationTime: Math.round(aggregationTime),
                    totalLatency: Math.round(totalLatency / messages.length), // Average latency
                    conflicts,
                    duplicatesRemoved
                }
            };
            // Store result and emit event
            this.aggregationResults.set(sessionId, result);
            this.emit('aggregationComplete', sessionId, result);
            // Record metrics
            this.metricsService.recordMetric('aggregation_time', aggregationTime, 'ms', 'latency');
            this.metricsService.recordMetric('aggregation_total_session_time', totalSessionTime, 'ms', 'latency');
            this.metricsService.recordMetric('aggregation_conflicts', conflicts, 'count', 'gauge');
            this.metricsService.recordMetric('aggregation_duplicates_removed', duplicatesRemoved, 'count', 'gauge');
            this.metricsService.recordMetric('aggregation_success', 1, 'count', 'counter');
            const totalPairs = allSpotData.reduce((sum, ex) => sum + ex.spot.length, 0) +
                allFuturesData.reduce((sum, ex) => sum + ex.futures.length, 0);
            console.log(`🎯 AGGREGATION COMPLETE: ${totalPairs} pairs aggregated in ${aggregationTime.toFixed(1)}ms (${conflicts} conflicts, ${duplicatesRemoved} duplicates removed)`);
            // Cleanup
            this.cleanupSession(sessionId);
        }
        catch (error) {
            console.error(`❌ Aggregation failed for session ${sessionId}:`, error);
            this.metricsService.recordMetric('aggregation_errors', 1, 'count', 'counter');
            this.emit('aggregationError', sessionId, error);
            this.cleanupSession(sessionId);
        }
    }
    /**
     * 🌍 STREAMING: Add data with deduplication and conflict resolution
     */
    addDataWithDeduplication(existingData, newData) {
        let duplicatesRemoved = 0;
        let conflicts = 0;
        // Create hash for deduplication
        const dataHash = this.createDataHash(newData);
        if (this.seenDataHashes.has(dataHash)) {
            duplicatesRemoved++;
            return { newData: [], duplicatesRemoved, conflicts };
        }
        this.seenDataHashes.add(dataHash);
        // Check for conflicts with existing data
        const conflictingData = existingData.find(existing => existing.exchange === newData.exchange);
        if (conflictingData) {
            conflicts++;
            // Resolve conflict using resolution rules
            const resolvedData = this.resolveConflict(conflictingData, newData);
            // Replace conflicting data
            const index = existingData.indexOf(conflictingData);
            existingData[index] = resolvedData;
            return { newData: [], duplicatesRemoved, conflicts };
        }
        return { newData: [newData], duplicatesRemoved, conflicts };
    }
    /**
     * 🌍 STREAMING: Create hash for data deduplication
     */
    createDataHash(data) {
        const hashData = {
            exchange: data.exchange,
            spotCount: data.spot.length,
            futuresCount: data.futures.length,
            timestamp: Math.floor(data.timestamp / 10000) // Round to 10 seconds for deduplication
        };
        return Buffer.from(JSON.stringify(hashData)).toString('base64');
    }
    /**
     * 🌍 STREAMING: Resolve conflicts between data from different regions
     */
    resolveConflict(existing, incoming) {
        console.log(`🔄 Resolving conflict for ${existing.exchange} exchange`);
        const resolver = this.conflictResolutionRules.get(existing.exchange);
        if (resolver) {
            return resolver(existing, incoming);
        }
        // Default resolution: use most recent data
        return incoming.timestamp > existing.timestamp ? incoming : existing;
    }
    /**
     * 🌍 STREAMING: Setup conflict resolution rules for different exchanges
     */
    setupConflictResolutionRules() {
        // Gate.io: Prefer data with more pairs
        this.conflictResolutionRules.set('gateio', (existing, incoming) => {
            const existingTotal = existing.spot.length + existing.futures.length;
            const incomingTotal = incoming.spot.length + incoming.futures.length;
            return incomingTotal > existingTotal ? incoming : existing;
        });
        // MEXC: Prefer most recent data
        this.conflictResolutionRules.set('mexc', (existing, incoming) => {
            return incoming.timestamp > existing.timestamp ? incoming : existing;
        });
        // Bitget: Prefer data with better status
        this.conflictResolutionRules.set('bitget', (existing, incoming) => {
            if (incoming.status === 'success' && existing.status !== 'success') {
                return incoming;
            }
            if (existing.status === 'success' && incoming.status !== 'success') {
                return existing;
            }
            // Both have same status, prefer more recent
            return incoming.timestamp > existing.timestamp ? incoming : existing;
        });
        console.log('🔧 Conflict resolution rules configured for all exchanges');
    }
    /**
     * 🌍 STREAMING: Force complete session if timeout reached
     */
    forceCompleteSession(sessionId) {
        const messages = this.messageQueue.get(sessionId);
        const expectedRegions = this.expectedRegions.get(sessionId);
        if (!messages || !expectedRegions)
            return;
        const receivedRegions = messages.map(m => m.region);
        const missingRegions = expectedRegions.filter(region => !receivedRegions.includes(region));
        if (missingRegions.length > 0) {
            console.warn(`⏰ Session ${sessionId} timeout - missing regions: ${missingRegions.join(', ')}`);
            console.log(`🔄 Proceeding with partial aggregation (${receivedRegions.length}/${expectedRegions.length} regions)`);
            this.metricsService.recordMetric('aggregation_timeouts', 1, 'count', 'counter');
            this.metricsService.recordMetric('aggregation_partial_completion', receivedRegions.length / expectedRegions.length, 'percentage', 'gauge');
            if (messages.length > 0) {
                this.aggregateResults(sessionId);
            }
            else {
                console.error(`❌ Session ${sessionId} timeout with no messages received`);
                this.emit('aggregationError', sessionId, new Error('Session timeout with no data'));
                this.cleanupSession(sessionId);
            }
        }
    }
    /**
     * 🌍 STREAMING: Get aggregation result
     */
    getAggregationResult(sessionId) {
        return this.aggregationResults.get(sessionId) || null;
    }
    /**
     * 🌍 STREAMING: Cleanup session data
     */
    cleanupSession(sessionId) {
        this.messageQueue.delete(sessionId);
        this.expectedRegions.delete(sessionId);
        this.aggregationStartTimes.delete(sessionId);
        // Clean up old results (keep only last 10)
        if (this.aggregationResults.size > 10) {
            const oldestKey = this.aggregationResults.keys().next().value;
            this.aggregationResults.delete(oldestKey);
        }
        console.log(`🧹 Cleaned up session ${sessionId}`);
    }
    /**
     * 🌍 STREAMING: Start performance monitoring
     */
    startPerformanceMonitoring() {
        setInterval(() => {
            const queueSize = Array.from(this.messageQueue.values()).reduce((sum, messages) => sum + messages.length, 0);
            const activeSessions = this.messageQueue.size;
            const resultsCached = this.aggregationResults.size;
            this.metricsService.recordMetric('message_queue_size', queueSize, 'count', 'gauge');
            this.metricsService.recordMetric('active_aggregation_sessions', activeSessions, 'count', 'gauge');
            this.metricsService.recordMetric('cached_aggregation_results', resultsCached, 'count', 'gauge');
            if (queueSize > 100) {
                console.warn(`⚠️ Message queue growing large: ${queueSize} messages`);
            }
        }, 30000); // Every 30 seconds
    }
    /**
     * 🌍 STREAMING: Get performance statistics
     */
    getPerformanceStats() {
        const queueSize = Array.from(this.messageQueue.values()).reduce((sum, messages) => sum + messages.length, 0);
        return {
            activeSessions: this.messageQueue.size,
            queueSize,
            cachedResults: this.aggregationResults.size,
            averageAggregationTime: 0 // Would need to track this over time
        };
    }
    /**
     * 🌍 STREAMING: Clear all data (for testing/reset)
     */
    clearAll() {
        this.messageQueue.clear();
        this.aggregationResults.clear();
        this.expectedRegions.clear();
        this.aggregationStartTimes.clear();
        this.seenDataHashes.clear();
        console.log('🧹 MessageStreamService cleared all data');
    }
}
//# sourceMappingURL=MessageStreamService.js.map