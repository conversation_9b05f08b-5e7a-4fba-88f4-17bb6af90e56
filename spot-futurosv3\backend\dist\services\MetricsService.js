export class MetricsService {
    static instance;
    metrics = [];
    maxMetrics = 10000;
    startTime = Date.now();
    requestTimes = [];
    errorCount = 0;
    totalRequests = 0;
    // 🚀 ADVANCED PERCENTILE TRACKING: Sliding window metrics for ultra-detailed monitoring
    slidingWindows = new Map();
    DEFAULT_WINDOW_SIZE = 1000;
    DEFAULT_WINDOW_DURATION = 300000; // 5 minutes
    PERCENTILE_ALERT_THRESHOLDS = {
        p99_latency_threshold: 1000, // 1s
        p95_latency_threshold: 800, // 800ms
        p90_latency_threshold: 500, // 500ms
        error_rate_threshold: 1 // 1%
    };
    // Performance monitoring intervals
    cleanupInterval = null;
    alertCheckInterval = null;
    constructor() {
        this.initializeAdvancedMetrics();
    }
    static getInstance() {
        if (!MetricsService.instance) {
            MetricsService.instance = new MetricsService();
        }
        return MetricsService.instance;
    }
    recordMetric(name, value, unit, category) {
        const metric = {
            name,
            value,
            unit,
            timestamp: Date.now(),
            category
        };
        this.metrics.unshift(metric);
        // <PERSON><PERSON> apenas as métricas mais recentes
        if (this.metrics.length > this.maxMetrics) {
            this.metrics = this.metrics.slice(0, this.maxMetrics);
        }
        // 🚀 ADVANCED PERCENTILE TRACKING: Add to sliding window for percentile calculations
        if (category === 'latency') {
            this.addToSlidingWindow(name, value);
        }
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Initialize advanced metrics system
     */
    initializeAdvancedMetrics() {
        // Initialize sliding windows for key metrics
        const keyMetrics = [
            'api_response_time',
            'data_processing_time',
            'cache_operation_time',
            'websocket_latency',
            'end_to_end_latency',
            'gateio_api_latency',
            'mexc_api_latency',
            'bitget_api_latency',
            'US-EAST_processing_time',
            'EU-WEST_processing_time',
            'ASIA_processing_time',
            'ultra_parallel_total_time',
            'regional_processing_total_time',
            'streaming_aggregation_total_time'
        ];
        keyMetrics.forEach(metricName => {
            this.slidingWindows.set(metricName, {
                windowSize: this.DEFAULT_WINDOW_SIZE,
                windowDuration: this.DEFAULT_WINDOW_DURATION,
                samples: [],
                percentiles: this.createEmptyPercentiles()
            });
        });
        // Start cleanup and alert monitoring
        this.startPerformanceMonitoring();
        console.log('🚀 Advanced percentile tracking initialized for ultra-low latency monitoring');
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Add sample to sliding window
     */
    addToSlidingWindow(metricName, value) {
        const window = this.slidingWindows.get(metricName);
        if (!window) {
            // Create new sliding window for unknown metrics
            this.slidingWindows.set(metricName, {
                windowSize: this.DEFAULT_WINDOW_SIZE,
                windowDuration: this.DEFAULT_WINDOW_DURATION,
                samples: [],
                percentiles: this.createEmptyPercentiles()
            });
            return this.addToSlidingWindow(metricName, value);
        }
        const now = Date.now();
        // Add new sample
        window.samples.push({ value, timestamp: now });
        // Remove old samples outside window duration
        const cutoffTime = now - window.windowDuration;
        window.samples = window.samples.filter(sample => sample.timestamp > cutoffTime);
        // Keep only the most recent samples within window size
        if (window.samples.length > window.windowSize) {
            window.samples = window.samples.slice(-window.windowSize);
        }
        // Recalculate percentiles
        window.percentiles = this.calculatePercentiles(window.samples.map(s => s.value));
        window.percentiles.lastUpdated = now;
        // Check for alerts
        this.checkPercentileAlerts(metricName, window.percentiles);
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Calculate percentiles from samples
     */
    calculatePercentiles(values) {
        if (values.length === 0) {
            return this.createEmptyPercentiles();
        }
        const sorted = [...values].sort((a, b) => a - b);
        const count = sorted.length;
        const getPercentile = (p) => {
            const index = Math.ceil((p / 100) * count) - 1;
            return sorted[Math.max(0, Math.min(index, count - 1))];
        };
        const sum = sorted.reduce((a, b) => a + b, 0);
        const mean = sum / count;
        return {
            p50: getPercentile(50),
            p90: getPercentile(90),
            p95: getPercentile(95),
            p99: getPercentile(99),
            p999: getPercentile(99.9),
            min: sorted[0],
            max: sorted[count - 1],
            mean: Math.round(mean * 100) / 100,
            count,
            lastUpdated: Date.now()
        };
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Create empty percentiles structure
     */
    createEmptyPercentiles() {
        return {
            p50: 0,
            p90: 0,
            p95: 0,
            p99: 0,
            p999: 0,
            min: 0,
            max: 0,
            mean: 0,
            count: 0,
            lastUpdated: Date.now()
        };
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Check for performance alerts
     */
    checkPercentileAlerts(metricName, percentiles) {
        const alerts = [];
        // P99 latency alert
        if (percentiles.p99 > this.PERCENTILE_ALERT_THRESHOLDS.p99_latency_threshold) {
            alerts.push(`🚨 P99 LATENCY ALERT: ${metricName} = ${percentiles.p99}ms (threshold: ${this.PERCENTILE_ALERT_THRESHOLDS.p99_latency_threshold}ms)`);
        }
        // P95 latency alert
        if (percentiles.p95 > this.PERCENTILE_ALERT_THRESHOLDS.p95_latency_threshold) {
            alerts.push(`⚠️ P95 LATENCY WARNING: ${metricName} = ${percentiles.p95}ms (threshold: ${this.PERCENTILE_ALERT_THRESHOLDS.p95_latency_threshold}ms)`);
        }
        // P90 latency alert
        if (percentiles.p90 > this.PERCENTILE_ALERT_THRESHOLDS.p90_latency_threshold) {
            alerts.push(`⚠️ P90 LATENCY WARNING: ${metricName} = ${percentiles.p90}ms (threshold: ${this.PERCENTILE_ALERT_THRESHOLDS.p90_latency_threshold}ms)`);
        }
        // Log alerts
        alerts.forEach(alert => {
            console.warn(alert);
            this.recordMetric(`${metricName}_alert`, 1, 'count', 'error');
        });
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Start performance monitoring intervals
     */
    startPerformanceMonitoring() {
        // Cleanup old samples every minute
        this.cleanupInterval = setInterval(() => {
            this.cleanupSlidingWindows();
        }, 60000);
        // Check alerts every 30 seconds
        this.alertCheckInterval = setInterval(() => {
            this.performPeriodicAlertCheck();
        }, 30000);
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Cleanup old samples from sliding windows
     */
    cleanupSlidingWindows() {
        const now = Date.now();
        let totalSamplesRemoved = 0;
        this.slidingWindows.forEach((window, metricName) => {
            const initialCount = window.samples.length;
            const cutoffTime = now - window.windowDuration;
            window.samples = window.samples.filter(sample => sample.timestamp > cutoffTime);
            const removedCount = initialCount - window.samples.length;
            totalSamplesRemoved += removedCount;
            // Recalculate percentiles if samples were removed
            if (removedCount > 0) {
                window.percentiles = this.calculatePercentiles(window.samples.map(s => s.value));
                window.percentiles.lastUpdated = now;
            }
        });
        if (totalSamplesRemoved > 0) {
            console.log(`🧹 Cleaned up ${totalSamplesRemoved} old samples from sliding windows`);
        }
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Perform periodic alert checks
     */
    performPeriodicAlertCheck() {
        const criticalMetrics = ['api_response_time', 'end_to_end_latency', 'ultra_parallel_total_time'];
        criticalMetrics.forEach(metricName => {
            const window = this.slidingWindows.get(metricName);
            if (window && window.samples.length > 10) { // Only check if we have enough samples
                // Check if P99 is consistently high
                if (window.percentiles.p99 > this.PERCENTILE_ALERT_THRESHOLDS.p99_latency_threshold) {
                    console.error(`🚨 CRITICAL: ${metricName} P99 latency consistently high: ${window.percentiles.p99}ms`);
                    this.recordMetric(`${metricName}_critical_alert`, window.percentiles.p99, 'ms', 'error');
                }
                // Check if mean is trending upward
                const recentSamples = window.samples.slice(-100); // Last 100 samples
                if (recentSamples.length >= 50) {
                    const firstHalf = recentSamples.slice(0, 25);
                    const secondHalf = recentSamples.slice(-25);
                    const firstHalfMean = firstHalf.reduce((a, b) => a + b.value, 0) / firstHalf.length;
                    const secondHalfMean = secondHalf.reduce((a, b) => a + b.value, 0) / secondHalf.length;
                    if (secondHalfMean > firstHalfMean * 1.5) { // 50% increase
                        console.warn(`📈 TREND ALERT: ${metricName} latency trending upward: ${firstHalfMean.toFixed(1)}ms → ${secondHalfMean.toFixed(1)}ms`);
                        this.recordMetric(`${metricName}_trend_alert`, secondHalfMean - firstHalfMean, 'ms', 'error');
                    }
                }
            }
        });
    }
    recordApiCall(responseTime, isError = false) {
        this.totalRequests++;
        if (isError) {
            this.errorCount++;
            this.recordMetric('api_error', 1, 'count', 'error');
        }
        else {
            this.recordMetric('api_response_time', responseTime, 'ms', 'latency');
        }
        // Manter apenas os últimos 1000 tempos de resposta para cálculos
        this.requestTimes.unshift(responseTime);
        if (this.requestTimes.length > 1000) {
            this.requestTimes = this.requestTimes.slice(0, 1000);
        }
    }
    recordBusinessMetric(name, value, unit) {
        this.recordMetric(name, value, unit, 'business');
    }
    getMetrics(category, limit = 100) {
        let filteredMetrics = this.metrics;
        if (category) {
            filteredMetrics = this.metrics.filter(m => m.category === category);
        }
        return filteredMetrics.slice(0, limit);
    }
    getSystemHealth() {
        const now = Date.now();
        const uptime = now - this.startTime;
        // Calcular uso de memória
        const memUsage = process.memoryUsage();
        const memoryUsage = {
            used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
            total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
            percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
        };
        // Calcular tempos de resposta da API
        const sortedTimes = [...this.requestTimes].sort((a, b) => a - b);
        const apiResponseTimes = {
            average: sortedTimes.length > 0 ? Math.round(sortedTimes.reduce((a, b) => a + b, 0) / sortedTimes.length) : 0,
            p95: sortedTimes.length > 0 ? sortedTimes[Math.floor(sortedTimes.length * 0.95)] || 0 : 0,
            p99: sortedTimes.length > 0 ? sortedTimes[Math.floor(sortedTimes.length * 0.99)] || 0 : 0
        };
        // Calcular taxa de erro
        const errorRate = this.totalRequests > 0 ? (this.errorCount / this.totalRequests) * 100 : 0;
        // Determinar status de saúde
        let status = 'healthy';
        if (memoryUsage.percentage > 90 || apiResponseTimes.average > 5000 || errorRate > 10) {
            status = 'unhealthy';
        }
        else if (memoryUsage.percentage > 70 || apiResponseTimes.average > 2000 || errorRate > 5) {
            status = 'degraded';
        }
        return {
            status,
            uptime,
            memoryUsage,
            apiResponseTimes,
            errorRate: Math.round(errorRate * 100) / 100,
            lastUpdate: now
        };
    }
    getBusinessMetrics() {
        const businessMetrics = this.metrics.filter(m => m.category === 'business');
        // Métricas de oportunidades
        const opportunityMetrics = businessMetrics.filter(m => m.name === 'opportunities_found');
        const totalOpportunities = opportunityMetrics.reduce((sum, m) => sum + m.value, 0);
        // Métricas de spread
        const spreadMetrics = businessMetrics.filter(m => m.name === 'average_spread');
        const averageSpread = spreadMetrics.length > 0
            ? spreadMetrics.reduce((sum, m) => sum + m.value, 0) / spreadMetrics.length
            : 0;
        // Top exchanges e símbolos (simulado - seria melhor ter dados reais)
        const topExchanges = [
            { exchange: 'gateio', count: Math.floor(totalOpportunities * 0.4) },
            { exchange: 'mexc', count: Math.floor(totalOpportunities * 0.35) },
            { exchange: 'bitget', count: Math.floor(totalOpportunities * 0.25) }
        ];
        const topSymbols = [
            { symbol: 'BTC/USDT', count: Math.floor(totalOpportunities * 0.15) },
            { symbol: 'ETH/USDT', count: Math.floor(totalOpportunities * 0.12) },
            { symbol: 'BNB/USDT', count: Math.floor(totalOpportunities * 0.08) },
            { symbol: 'SOL/USDT', count: Math.floor(totalOpportunities * 0.06) },
            { symbol: 'ADA/USDT', count: Math.floor(totalOpportunities * 0.05) }
        ];
        return {
            totalOpportunities,
            averageSpread: Math.round(averageSpread * 1000) / 1000,
            topExchanges,
            topSymbols
        };
    }
    getLatencyMetrics() {
        const latencyMetrics = this.metrics.filter(m => m.category === 'latency');
        // Métricas de API
        const apiMetrics = latencyMetrics.filter(m => m.name === 'api_response_time');
        const apiTimes = apiMetrics.map(m => m.value).sort((a, b) => a - b);
        // Métricas de processamento
        const processingMetrics = latencyMetrics.filter(m => m.name === 'data_processing_time');
        const processingTimes = processingMetrics.map(m => m.value).sort((a, b) => a - b);
        // Cache hit rate (simulado)
        const cacheHitRate = 95.5; // Seria calculado baseado em métricas reais
        return {
            apiCalls: {
                average: apiTimes.length > 0 ? Math.round(apiTimes.reduce((a, b) => a + b, 0) / apiTimes.length) : 0,
                p95: apiTimes.length > 0 ? apiTimes[Math.floor(apiTimes.length * 0.95)] || 0 : 0,
                p99: apiTimes.length > 0 ? apiTimes[Math.floor(apiTimes.length * 0.99)] || 0 : 0
            },
            dataProcessing: {
                average: processingTimes.length > 0 ? Math.round(processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length) : 0,
                p95: processingTimes.length > 0 ? processingTimes[Math.floor(processingTimes.length * 0.95)] || 0 : 0,
                p99: processingTimes.length > 0 ? processingTimes[Math.floor(processingTimes.length * 0.99)] || 0 : 0
            },
            cacheHitRate
        };
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Get comprehensive percentile metrics
     */
    getAdvancedLatencyMetrics() {
        const getMetricsForName = (name) => {
            const window = this.slidingWindows.get(name);
            return window ? window.percentiles : this.createEmptyPercentiles();
        };
        return {
            apiCalls: getMetricsForName('api_response_time'),
            dataProcessing: getMetricsForName('data_processing_time'),
            cacheOperations: getMetricsForName('cache_operation_time'),
            websocketLatency: getMetricsForName('websocket_latency'),
            endToEndLatency: getMetricsForName('end_to_end_latency'),
            regionalProcessing: {
                'US-EAST': getMetricsForName('US-EAST_processing_time'),
                'EU-WEST': getMetricsForName('EU-WEST_processing_time'),
                'ASIA': getMetricsForName('ASIA_processing_time')
            },
            exchangeLatency: {
                'gateio': getMetricsForName('gateio_api_latency'),
                'mexc': getMetricsForName('mexc_api_latency'),
                'bitget': getMetricsForName('bitget_api_latency')
            }
        };
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Get percentile metrics for specific metric
     */
    getPercentileMetrics(metricName) {
        const window = this.slidingWindows.get(metricName);
        return window ? window.percentiles : null;
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Get all sliding window metrics
     */
    getAllSlidingWindowMetrics() {
        return new Map(this.slidingWindows);
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Get sliding windows as array for API
     */
    getSlidingWindows() {
        const windows = [];
        this.slidingWindows.forEach((window, name) => {
            windows.push({
                name,
                windowSize: window.windowSize,
                windowDuration: window.windowDuration,
                sampleCount: window.samples.length,
                percentiles: window.percentiles
            });
        });
        return windows.sort((a, b) => b.sampleCount - a.sampleCount);
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Get performance summary with percentiles
     */
    getPerformanceSummary() {
        const alertMetrics = this.metrics.filter(m => m.name.includes('_alert'));
        const alertsTriggered = alertMetrics.length;
        // Calculate overall latency percentiles
        const allLatencySamples = [];
        const metricPerformance = {};
        this.slidingWindows.forEach((window, name) => {
            if (window.samples.length > 0) {
                allLatencySamples.push(...window.samples.map(s => s.value));
                metricPerformance[name] = window.percentiles.p95;
            }
        });
        const overallPercentiles = this.calculatePercentiles(allLatencySamples);
        // Determine system health
        let systemHealth = 'excellent';
        if (overallPercentiles.p99 > 1000 || alertsTriggered > 10) {
            systemHealth = 'critical';
        }
        else if (overallPercentiles.p95 > 800 || alertsTriggered > 5) {
            systemHealth = 'degraded';
        }
        else if (overallPercentiles.p90 > 500 || alertsTriggered > 2) {
            systemHealth = 'good';
        }
        // Find best and worst performers
        const sortedPerformance = Object.entries(metricPerformance).sort(([, a], [, b]) => a - b);
        const bestPerformer = sortedPerformance[0]?.[0] || 'none';
        const worstPerformer = sortedPerformance[sortedPerformance.length - 1]?.[0] || 'none';
        // Analyze trends (simplified)
        const improving = [];
        const degrading = [];
        const stable = [];
        this.slidingWindows.forEach((window, name) => {
            if (window.samples.length >= 20) {
                const recent = window.samples.slice(-10);
                const older = window.samples.slice(-20, -10);
                const recentAvg = recent.reduce((a, b) => a + b.value, 0) / recent.length;
                const olderAvg = older.reduce((a, b) => a + b.value, 0) / older.length;
                const change = (recentAvg - olderAvg) / olderAvg;
                if (change < -0.1)
                    improving.push(name);
                else if (change > 0.1)
                    degrading.push(name);
                else
                    stable.push(name);
            }
        });
        return {
            overview: {
                totalMetrics: this.metrics.length,
                activeWindows: this.slidingWindows.size,
                alertsTriggered,
                systemHealth
            },
            latencyBreakdown: {
                p50: overallPercentiles.p50,
                p90: overallPercentiles.p90,
                p95: overallPercentiles.p95,
                p99: overallPercentiles.p99,
                worstPerformer,
                bestPerformer
            },
            trends: {
                improving,
                degrading,
                stable
            }
        };
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Record latency with automatic percentile tracking
     */
    recordLatency(metricName, latencyMs, context) {
        // Record standard metric
        this.recordMetric(metricName, latencyMs, 'ms', 'latency');
        // Add context-specific tracking
        if (context) {
            Object.entries(context).forEach(([key, value]) => {
                if (typeof value === 'number') {
                    this.recordMetric(`${metricName}_${key}`, value, 'ms', 'latency');
                }
            });
        }
        // Log performance if above thresholds
        if (latencyMs > this.PERCENTILE_ALERT_THRESHOLDS.p99_latency_threshold) {
            console.warn(`🚨 HIGH LATENCY: ${metricName} = ${latencyMs}ms ${context ? JSON.stringify(context) : ''}`);
        }
    }
    clearMetrics() {
        this.metrics = [];
        this.requestTimes = [];
        this.errorCount = 0;
        this.totalRequests = 0;
        // 🚀 ADVANCED PERCENTILE TRACKING: Clear sliding windows
        this.slidingWindows.clear();
        this.initializeAdvancedMetrics();
    }
    /**
     * 🚀 ADVANCED PERCENTILE TRACKING: Cleanup resources
     */
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        if (this.alertCheckInterval) {
            clearInterval(this.alertCheckInterval);
            this.alertCheckInterval = null;
        }
        this.slidingWindows.clear();
        console.log('🧹 MetricsService destroyed and resources cleaned up');
    }
    getMetricsSummary() {
        const categories = this.metrics.reduce((acc, metric) => {
            acc[metric.category] = (acc[metric.category] || 0) + 1;
            return acc;
        }, {});
        const timestamps = this.metrics.map(m => m.timestamp);
        const timeRange = {
            start: timestamps.length > 0 ? Math.min(...timestamps) : 0,
            end: timestamps.length > 0 ? Math.max(...timestamps) : 0
        };
        return {
            totalMetrics: this.metrics.length,
            categories,
            timeRange,
            memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024)
        };
    }
}
//# sourceMappingURL=MetricsService.js.map