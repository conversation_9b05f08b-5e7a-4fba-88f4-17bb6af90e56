/**
 * 🚀 ULTRA-WEBSOCKET: OpportunityBroadcaster
 *
 * Serviço de broadcast de oportunidades de arbitragem via WebSocket
 * com otimizações ultra baixa latência.
 */
import { WebSocket, WebSocketServer } from 'ws';
export class OpportunityBroadcaster {
    clients = new Map();
    wss = null;
    stats;
    messageQueue = [];
    startTime;
    latencyMeasurements = [];
    constructor() {
        this.startTime = Date.now();
        this.stats = {
            connectedClients: 0,
            totalConnections: 0,
            messagesSent: 0,
            messagesReceived: 0,
            averageLatency: 0,
            queueLength: 0,
            uptime: 0,
            errors: 0
        };
        // Cleanup de latência a cada 5 minutos
        setInterval(() => {
            if (this.latencyMeasurements.length > 1000) {
                this.latencyMeasurements = this.latencyMeasurements.slice(-500);
            }
        }, 5 * 60 * 1000);
    }
    /**
     * Inicializar WebSocket Server
     */
    initialize(server) {
        this.wss = new WebSocketServer({ server, path: '/ws' });
        this.wss.on('listening', () => {
            console.log('🚀 WebSocket Server is listening on /ws');
        });
        this.wss.on('error', (error) => {
            console.error('❌ WebSocket Server error:', error);
            this.stats.errors++;
        });
        this.wss.on('connection', (ws, req) => {
            const clientId = this.generateClientId();
            const client = {
                ws,
                id: clientId,
                connectedAt: Date.now(),
                lastPing: Date.now(),
                subscriptions: new Set()
            };
            this.clients.set(clientId, client);
            this.stats.connectedClients = this.clients.size;
            this.stats.totalConnections++;
            console.log(`🔌 Cliente WebSocket conectado: ${clientId} (Total: ${this.clients.size})`);
            // Enviar mensagem de boas-vindas
            this.sendToClient(clientId, {
                type: 'welcome',
                clientId,
                timestamp: Date.now(),
                message: 'Conectado ao sistema de arbitragem ultra baixa latência'
            });
            // Handlers de mensagens
            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    this.handleClientMessage(clientId, message);
                    this.stats.messagesReceived++;
                }
                catch (error) {
                    console.error(`❌ Erro ao processar mensagem do cliente ${clientId}:`, error);
                    this.stats.errors++;
                }
            });
            // Handler de desconexão
            ws.on('close', () => {
                this.clients.delete(clientId);
                this.stats.connectedClients = this.clients.size;
                console.log(`🔌 Cliente WebSocket desconectado: ${clientId} (Total: ${this.clients.size})`);
            });
            // Handler de erro
            ws.on('error', (error) => {
                console.error(`❌ Erro WebSocket cliente ${clientId}:`, error);
                this.stats.errors++;
                this.clients.delete(clientId);
                this.stats.connectedClients = this.clients.size;
            });
            // Ping/Pong para manter conexão viva
            const pingInterval = setInterval(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    try {
                        ws.ping();
                        client.lastPing = Date.now();
                    }
                    catch (error) {
                        console.error(`❌ Erro ao enviar ping para cliente ${clientId}:`, error);
                        clearInterval(pingInterval);
                        this.clients.delete(clientId);
                    }
                }
                else {
                    clearInterval(pingInterval);
                }
            }, 45000); // Ping a cada 45 segundos (menos agressivo)
            // Pong handler
            ws.on('pong', () => {
                client.lastPing = Date.now();
            });
        });
        console.log('🚀 WebSocket Server inicializado para broadcast de oportunidades');
    }
    /**
     * Broadcast de oportunidade para todos os clientes
     */
    broadcast(opportunity) {
        if (this.clients.size === 0)
            return;
        const message = {
            type: 'opportunity',
            data: opportunity,
            timestamp: Date.now()
        };
        const messageStr = JSON.stringify(message);
        let successCount = 0;
        let errorCount = 0;
        this.clients.forEach((client, clientId) => {
            try {
                if (client.ws.readyState === WebSocket.OPEN) {
                    const sendStart = Date.now();
                    client.ws.send(messageStr);
                    // Medir latência (aproximada)
                    const latency = Date.now() - sendStart;
                    this.latencyMeasurements.push(latency);
                    successCount++;
                }
                else {
                    // Remover cliente desconectado
                    this.clients.delete(clientId);
                    errorCount++;
                }
            }
            catch (error) {
                console.error(`❌ Erro ao enviar para cliente ${clientId}:`, error);
                this.clients.delete(clientId);
                errorCount++;
                this.stats.errors++;
            }
        });
        this.stats.messagesSent += successCount;
        this.stats.connectedClients = this.clients.size;
        // Calcular latência média
        if (this.latencyMeasurements.length > 0) {
            this.stats.averageLatency = this.latencyMeasurements.reduce((a, b) => a + b, 0) / this.latencyMeasurements.length;
        }
        if (successCount > 0) {
            console.log(`📡 Oportunidade broadcast para ${successCount} clientes (${opportunity.symbol} - ${opportunity.spreadPercentage.toFixed(3)}%)`);
        }
    }
    /**
     * Enviar mensagem para cliente específico
     */
    sendToClient(clientId, message) {
        const client = this.clients.get(clientId);
        if (client && client.ws.readyState === WebSocket.OPEN) {
            try {
                client.ws.send(JSON.stringify(message));
                this.stats.messagesSent++;
            }
            catch (error) {
                console.error(`❌ Erro ao enviar mensagem para cliente ${clientId}:`, error);
                this.clients.delete(clientId);
                this.stats.errors++;
            }
        }
    }
    /**
     * Processar mensagem do cliente
     */
    handleClientMessage(clientId, message) {
        const client = this.clients.get(clientId);
        if (!client)
            return;
        switch (message.type) {
            case 'subscribe':
                if (message.symbols && Array.isArray(message.symbols)) {
                    message.symbols.forEach((symbol) => {
                        client.subscriptions.add(symbol.toUpperCase());
                    });
                    this.sendToClient(clientId, {
                        type: 'subscribed',
                        symbols: Array.from(client.subscriptions),
                        timestamp: Date.now()
                    });
                }
                break;
            case 'unsubscribe':
                if (message.symbols && Array.isArray(message.symbols)) {
                    message.symbols.forEach((symbol) => {
                        client.subscriptions.delete(symbol.toUpperCase());
                    });
                    this.sendToClient(clientId, {
                        type: 'unsubscribed',
                        symbols: message.symbols,
                        timestamp: Date.now()
                    });
                }
                break;
            case 'ping':
                this.sendToClient(clientId, {
                    type: 'pong',
                    timestamp: Date.now()
                });
                break;
            case 'getStats':
                this.sendToClient(clientId, {
                    type: 'stats',
                    data: this.getStats(),
                    timestamp: Date.now()
                });
                break;
            default:
                console.log(`📨 Mensagem não reconhecida do cliente ${clientId}:`, message.type);
        }
    }
    /**
     * Gerar ID único para cliente
     */
    generateClientId() {
        return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Obter estatísticas do broadcaster
     */
    getStats() {
        this.stats.uptime = Date.now() - this.startTime;
        this.stats.queueLength = this.messageQueue.length;
        return { ...this.stats };
    }
    /**
     * 🚀 ULTRA-OPTIMIZED: Broadcast only relevant opportunities (>0.3% spread)
     */
    broadcastMultiple(opportunities) {
        if (opportunities.length === 0 || this.clients.size === 0)
            return;
        const startTime = performance.now();
        // 🚀 FILTER: Only broadcast POSITIVE opportunities with significant spread (>0.3%)
        const relevantOpportunities = opportunities.filter(opp => {
            // ✅ CRÍTICO: Apenas spreads POSITIVOS (remove negativos)
            if (opp.spreadPercentage <= 0)
                return false;
            // ✅ Spread mínimo de 0.3%
            if (opp.spreadPercentage < 0.3)
                return false;
            // ✅ Spread máximo de 150%
            if (opp.spreadPercentage > 150)
                return false;
            // ✅ Preços válidos
            if (opp.spotPrice <= 0 || opp.futuresPrice <= 0)
                return false;
            // ✅ Volume mínimo
            return (opp.volume || 0) >= 1000;
        });
        if (relevantOpportunities.length === 0) {
            console.log('📊 No relevant opportunities to broadcast (all below 0.3% spread)');
            return;
        }
        // 🚀 SORT: Sort by spread percentage (highest first)
        relevantOpportunities.sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage));
        // ✅ SEM LIMITE: Broadcast todas as oportunidades relevantes
        const limitedOpportunities = relevantOpportunities;
        const message = {
            type: 'opportunities',
            data: limitedOpportunities,
            count: limitedOpportunities.length,
            total: opportunities.length,
            timestamp: Date.now(),
            batchId: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`
        };
        const messageStr = JSON.stringify(message);
        let successCount = 0;
        let errorCount = 0;
        // 🚀 OTIMIZADO: Usar Array.from para melhor performance
        const clientEntries = Array.from(this.clients.entries());
        for (const [clientId, client] of clientEntries) {
            try {
                if (client.ws.readyState === WebSocket.OPEN) {
                    client.ws.send(messageStr);
                    successCount++;
                }
                else {
                    // Cliente desconectado, remover
                    this.clients.delete(clientId);
                }
            }
            catch (error) {
                console.error(`❌ Erro ao enviar múltiplas oportunidades para cliente ${clientId}:`, error);
                this.clients.delete(clientId);
                this.stats.errors++;
                errorCount++;
            }
        }
        this.stats.messagesSent += successCount;
        this.stats.connectedClients = this.clients.size;
        const processingTime = performance.now() - startTime;
        if (processingTime > 50) {
            console.warn(`🚨 Broadcast lento: ${processingTime.toFixed(1)}ms (target: <50ms)`);
        }
        console.log(`📡 ${limitedOpportunities.length}/${opportunities.length} oportunidades relevantes (>0.3% spread) broadcast para ${successCount} clientes em ${processingTime.toFixed(1)}ms`);
        if (errorCount > 0) {
            console.warn(`⚠️ ${errorCount} erros durante broadcast`);
        }
    }
    /**
     * 🚀 STREAMING: Broadcast opportunities from streaming engine
     */
    async broadcastOpportunities(opportunities) {
        return new Promise((resolve) => {
            this.broadcastMultiple(opportunities);
            resolve();
        });
    }
    /**
     * 🚀 FILTER: Get broadcast-worthy opportunities count
     */
    getRelevantOpportunitiesCount(opportunities) {
        return opportunities.filter(opp => {
            const spread = Math.abs(opp.spreadPercentage);
            return spread >= 0.3 && spread <= 50 &&
                opp.spotPrice > 0 && opp.futuresPrice > 0 &&
                (opp.volume || 0) >= 1000;
        }).length;
    }
    /**
     * Limpar recursos
     */
    cleanup() {
        this.clients.forEach((client) => {
            if (client.ws.readyState === WebSocket.OPEN) {
                client.ws.close();
            }
        });
        this.clients.clear();
        if (this.wss) {
            this.wss.close();
        }
        console.log('🧹 OpportunityBroadcaster limpo');
    }
}
//# sourceMappingURL=OpportunityBroadcaster.js.map