// PerformanceMonitorService - Sistema de monitoramento de performance em tempo real
// Coleta métricas P90/P95/P99, throughput, error rate, memory usage
import os from 'os';
import { CacheService } from './CacheService.js';
import { UltraFastCache } from './UltraFastCache.js';
export class PerformanceMonitorService {
    static instance;
    latencyHistory = [];
    requestCount = 0;
    errorCount = 0;
    errorsByType = {};
    startTime = Date.now();
    alerts = [];
    metricsHistory = [];
    // 🚀 THRESHOLDS: Performance alert thresholds
    THRESHOLDS = {
        LATENCY_P95_WARNING: 1500, // 1.5s
        LATENCY_P95_CRITICAL: 2000, // 2s
        ERROR_RATE_WARNING: 1, // 1%
        ERROR_RATE_CRITICAL: 5, // 5%
        MEMORY_WARNING: 80, // 80%
        MEMORY_CRITICAL: 90, // 90%
        CACHE_HIT_RATE_WARNING: 70, // 70%
        CACHE_HIT_RATE_CRITICAL: 50, // 50%
        THROUGHPUT_WARNING: 10, // 10 req/s
        THROUGHPUT_CRITICAL: 5 // 5 req/s
    };
    static getInstance() {
        if (!PerformanceMonitorService.instance) {
            PerformanceMonitorService.instance = new PerformanceMonitorService();
            // Start monitoring
            PerformanceMonitorService.instance.startMonitoring();
        }
        return PerformanceMonitorService.instance;
    }
    /**
     * 🚀 MONITORING: Start continuous performance monitoring
     */
    startMonitoring() {
        // Collect metrics every 5 seconds
        setInterval(() => {
            this.collectMetrics();
        }, 5000);
        // Cleanup old data every minute
        setInterval(() => {
            this.cleanupOldData();
        }, 60000);
        console.log('🔍 PerformanceMonitor: Started continuous monitoring');
    }
    /**
     * 🚀 METRICS: Record request latency
     */
    recordLatency(latencyMs) {
        this.latencyHistory.push(latencyMs);
        this.requestCount++;
        // Keep only last 1000 requests for performance
        if (this.latencyHistory.length > 1000) {
            this.latencyHistory = this.latencyHistory.slice(-1000);
        }
    }
    /**
     * 🚀 METRICS: Record error
     */
    recordError(errorType) {
        this.errorCount++;
        this.errorsByType[errorType] = (this.errorsByType[errorType] || 0) + 1;
    }
    /**
     * 🚀 METRICS: Calculate percentiles
     */
    calculatePercentiles(values) {
        if (values.length === 0) {
            return { p50: 0, p90: 0, p95: 0, p99: 0 };
        }
        const sorted = [...values].sort((a, b) => a - b);
        const len = sorted.length;
        return {
            p50: sorted[Math.floor(len * 0.5)] || 0,
            p90: sorted[Math.floor(len * 0.9)] || 0,
            p95: sorted[Math.floor(len * 0.95)] || 0,
            p99: sorted[Math.floor(len * 0.99)] || 0
        };
    }
    /**
     * 🚀 METRICS: Get memory usage
     */
    getMemoryUsage() {
        const memUsage = process.memoryUsage();
        const totalMB = (memUsage.heapTotal + memUsage.external) / 1024 / 1024;
        const usedMB = (memUsage.heapUsed + memUsage.external) / 1024 / 1024;
        return {
            usedMB: Math.round(usedMB * 100) / 100,
            totalMB: Math.round(totalMB * 100) / 100,
            percentage: Math.round((usedMB / totalMB) * 10000) / 100,
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal
        };
    }
    /**
     * 🚀 METRICS: Get system metrics
     */
    getSystemMetrics() {
        return {
            cpuUsage: Math.round(process.cpuUsage().user / 1000000 * 100) / 100, // Convert to percentage
            loadAverage: os.loadavg(),
            uptime: Math.round(process.uptime())
        };
    }
    /**
     * 🚀 METRICS: Collect comprehensive performance metrics
     */
    collectMetrics() {
        const now = Date.now();
        const timeWindow = 60000; // 1 minute window
        // Filter recent latencies
        const recentLatencies = this.latencyHistory.filter(l => l > 0);
        const percentiles = this.calculatePercentiles(recentLatencies);
        // Calculate throughput
        const requestsPerSecond = this.requestCount / ((now - this.startTime) / 1000);
        // Calculate error rate
        const totalRequests = this.requestCount;
        const errorRate = totalRequests > 0 ? (this.errorCount / totalRequests) * 100 : 0;
        // Get cache metrics (if available)
        const cacheStats = CacheService.getInstance().getStats();
        const ultraStats = UltraFastCache.getInstance().getStats();
        const metrics = {
            timestamp: now,
            latency: {
                ...percentiles,
                average: recentLatencies.length > 0 ? recentLatencies.reduce((sum, l) => sum + l, 0) / recentLatencies.length : 0,
                max: recentLatencies.length > 0 ? Math.max(...recentLatencies) : 0
            },
            throughput: {
                requestsPerSecond: Math.round(requestsPerSecond * 100) / 100,
                opportunitiesPerSecond: Math.round(requestsPerSecond * 100) / 100, // Approximate
                totalRequests: totalRequests
            },
            errorRate: {
                percentage: Math.round(errorRate * 100) / 100,
                totalErrors: this.errorCount,
                errorsByType: { ...this.errorsByType }
            },
            memory: this.getMemoryUsage(),
            cache: {
                hitRate: Math.max(cacheStats.hitRate || 0, ultraStats.hitRate || 0),
                totalHits: (cacheStats.hitCount || 0) + (ultraStats.hits.total || 0),
                totalMisses: (cacheStats.missCount || 0) + (ultraStats.misses || 0),
                size: (cacheStats.size || 0) + (ultraStats.sizes.total || 0)
            },
            system: this.getSystemMetrics()
        };
        // Store metrics
        this.metricsHistory.push(metrics);
        // Check for alerts
        this.checkAlerts(metrics);
        // Log performance summary
        if (recentLatencies.length > 0) {
            console.log(`🔍 Performance: P95=${percentiles.p95}ms, Throughput=${requestsPerSecond.toFixed(1)} req/s, Errors=${errorRate.toFixed(2)}%, Memory=${metrics.memory.percentage}%`);
        }
    }
    /**
     * 🚀 ALERTS: Check performance thresholds and generate alerts
     */
    checkAlerts(metrics) {
        const alerts = [];
        // Latency alerts
        if (metrics.latency.p95 > this.THRESHOLDS.LATENCY_P95_CRITICAL) {
            alerts.push({
                id: `latency_critical_${Date.now()}`,
                type: 'LATENCY',
                severity: 'CRITICAL',
                message: `P95 latency is critically high: ${metrics.latency.p95}ms`,
                value: metrics.latency.p95,
                threshold: this.THRESHOLDS.LATENCY_P95_CRITICAL,
                timestamp: metrics.timestamp
            });
        }
        else if (metrics.latency.p95 > this.THRESHOLDS.LATENCY_P95_WARNING) {
            alerts.push({
                id: `latency_warning_${Date.now()}`,
                type: 'LATENCY',
                severity: 'HIGH',
                message: `P95 latency is high: ${metrics.latency.p95}ms`,
                value: metrics.latency.p95,
                threshold: this.THRESHOLDS.LATENCY_P95_WARNING,
                timestamp: metrics.timestamp
            });
        }
        // Error rate alerts
        if (metrics.errorRate.percentage > this.THRESHOLDS.ERROR_RATE_CRITICAL) {
            alerts.push({
                id: `error_critical_${Date.now()}`,
                type: 'ERROR_RATE',
                severity: 'CRITICAL',
                message: `Error rate is critically high: ${metrics.errorRate.percentage}%`,
                value: metrics.errorRate.percentage,
                threshold: this.THRESHOLDS.ERROR_RATE_CRITICAL,
                timestamp: metrics.timestamp
            });
        }
        else if (metrics.errorRate.percentage > this.THRESHOLDS.ERROR_RATE_WARNING) {
            alerts.push({
                id: `error_warning_${Date.now()}`,
                type: 'ERROR_RATE',
                severity: 'HIGH',
                message: `Error rate is high: ${metrics.errorRate.percentage}%`,
                value: metrics.errorRate.percentage,
                threshold: this.THRESHOLDS.ERROR_RATE_WARNING,
                timestamp: metrics.timestamp
            });
        }
        // Memory alerts
        if (metrics.memory.percentage > this.THRESHOLDS.MEMORY_CRITICAL) {
            alerts.push({
                id: `memory_critical_${Date.now()}`,
                type: 'MEMORY',
                severity: 'CRITICAL',
                message: `Memory usage is critically high: ${metrics.memory.percentage}%`,
                value: metrics.memory.percentage,
                threshold: this.THRESHOLDS.MEMORY_CRITICAL,
                timestamp: metrics.timestamp
            });
        }
        else if (metrics.memory.percentage > this.THRESHOLDS.MEMORY_WARNING) {
            alerts.push({
                id: `memory_warning_${Date.now()}`,
                type: 'MEMORY',
                severity: 'HIGH',
                message: `Memory usage is high: ${metrics.memory.percentage}%`,
                value: metrics.memory.percentage,
                threshold: this.THRESHOLDS.MEMORY_WARNING,
                timestamp: metrics.timestamp
            });
        }
        // Cache hit rate alerts
        if (metrics.cache.hitRate < this.THRESHOLDS.CACHE_HIT_RATE_CRITICAL) {
            alerts.push({
                id: `cache_critical_${Date.now()}`,
                type: 'CACHE',
                severity: 'CRITICAL',
                message: `Cache hit rate is critically low: ${metrics.cache.hitRate}%`,
                value: metrics.cache.hitRate,
                threshold: this.THRESHOLDS.CACHE_HIT_RATE_CRITICAL,
                timestamp: metrics.timestamp
            });
        }
        else if (metrics.cache.hitRate < this.THRESHOLDS.CACHE_HIT_RATE_WARNING) {
            alerts.push({
                id: `cache_warning_${Date.now()}`,
                type: 'CACHE',
                severity: 'HIGH',
                message: `Cache hit rate is low: ${metrics.cache.hitRate}%`,
                value: metrics.cache.hitRate,
                threshold: this.THRESHOLDS.CACHE_HIT_RATE_WARNING,
                timestamp: metrics.timestamp
            });
        }
        // Store alerts
        this.alerts.push(...alerts);
        // Log critical alerts
        alerts.forEach(alert => {
            if (alert.severity === 'CRITICAL') {
                console.error(`🚨 CRITICAL ALERT: ${alert.message}`);
            }
            else if (alert.severity === 'HIGH') {
                console.warn(`⚠️ HIGH ALERT: ${alert.message}`);
            }
        });
    }
    /**
     * 🚀 CLEANUP: Remove old data to prevent memory leaks
     */
    cleanupOldData() {
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        // Keep only last hour of metrics
        this.metricsHistory = this.metricsHistory.filter(m => m.timestamp > oneHourAgo);
        // Keep only last hour of alerts
        this.alerts = this.alerts.filter(a => a.timestamp > oneHourAgo);
        // Reset counters periodically to prevent overflow
        if (this.requestCount > 1000000) {
            this.requestCount = Math.floor(this.requestCount / 2);
            this.errorCount = Math.floor(this.errorCount / 2);
        }
    }
    /**
     * 🚀 API: Get current performance metrics
     */
    getCurrentMetrics() {
        return this.metricsHistory.length > 0 ? this.metricsHistory[this.metricsHistory.length - 1] : null;
    }
    /**
     * 🚀 API: Get metrics history
     */
    getMetricsHistory(minutes = 60) {
        const cutoff = Date.now() - (minutes * 60 * 1000);
        return this.metricsHistory.filter(m => m.timestamp > cutoff);
    }
    /**
     * 🚀 API: Get active alerts
     */
    getActiveAlerts() {
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        return this.alerts.filter(a => a.timestamp > oneHourAgo);
    }
    /**
     * 🚀 API: Get performance summary
     */
    getPerformanceSummary() {
        const currentMetrics = this.getCurrentMetrics();
        const activeAlerts = this.getActiveAlerts();
        const criticalAlerts = activeAlerts.filter(a => a.severity === 'CRITICAL').length;
        let status = 'HEALTHY';
        if (criticalAlerts > 0) {
            status = 'CRITICAL';
        }
        else if (activeAlerts.length > 0) {
            status = 'WARNING';
        }
        return {
            status,
            metrics: currentMetrics,
            activeAlerts: activeAlerts.length,
            criticalAlerts
        };
    }
}
//# sourceMappingURL=PerformanceMonitorService.js.map