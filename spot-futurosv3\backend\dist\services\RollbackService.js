import { MetricsService } from './MetricsService.js';
import { FeatureFlagService } from './FeatureFlagService.js';
import { AlertService } from './AlertService.js';
/**
 * 🔄 ROLLBACK: Automated rollback system for performance degradation
 * Monitors metrics and automatically rolls back optimizations when issues are detected
 */
export class RollbackService {
    static instance;
    metricsService;
    featureFlagService;
    alertService;
    triggers = new Map();
    executions = [];
    monitoringInterval = null;
    MAX_EXECUTIONS_HISTORY = 100;
    // 🔄 DEFAULT ROLLBACK TRIGGERS: Critical performance thresholds
    DEFAULT_TRIGGERS = [
        {
            id: 'p99-latency-critical',
            name: 'P99 Latency Critical',
            description: 'Rollback when P99 latency exceeds 2 seconds',
            enabled: true,
            conditions: {
                metricName: 'api_response_time',
                threshold: 2000,
                operator: 'gt',
                duration: 60000, // 1 minute
                consecutiveFailures: 3
            },
            actions: [
                {
                    type: 'disable_feature',
                    target: 'regional-processing',
                    priority: 1
                },
                {
                    type: 'disable_feature',
                    target: 'message-streaming',
                    priority: 2
                },
                {
                    type: 'alert',
                    target: 'performance-degradation',
                    priority: 3
                }
            ]
        },
        {
            id: 'error-rate-high',
            name: 'High Error Rate',
            description: 'Rollback when error rate exceeds 5%',
            enabled: true,
            conditions: {
                metricName: 'api_error_rate',
                threshold: 5,
                operator: 'gt',
                duration: 30000, // 30 seconds
                consecutiveFailures: 2
            },
            actions: [
                {
                    type: 'set_rollout',
                    target: 'ultra-parallel-processing',
                    value: 50, // Reduce rollout to 50%
                    priority: 1
                },
                {
                    type: 'alert',
                    target: 'high-error-rate',
                    priority: 2
                }
            ]
        },
        {
            id: 'cache-hit-rate-low',
            name: 'Low Cache Hit Rate',
            description: 'Rollback when cache hit rate drops below 90%',
            enabled: true,
            conditions: {
                metricName: 'cache_hit_rate',
                threshold: 90,
                operator: 'lt',
                duration: 120000, // 2 minutes
                consecutiveFailures: 5
            },
            actions: [
                {
                    type: 'disable_feature',
                    target: 'multi-layer-cache',
                    priority: 1
                },
                {
                    type: 'alert',
                    target: 'cache-performance-degradation',
                    priority: 2
                }
            ]
        },
        {
            id: 'websocket-connection-failures',
            name: 'WebSocket Connection Failures',
            description: 'Rollback when WebSocket connections fail frequently',
            enabled: true,
            conditions: {
                metricName: 'websocket_connection_errors',
                threshold: 10,
                operator: 'gt',
                duration: 60000, // 1 minute
                consecutiveFailures: 3
            },
            actions: [
                {
                    type: 'disable_feature',
                    target: 'websocket-streaming',
                    priority: 1
                },
                {
                    type: 'alert',
                    target: 'websocket-failures',
                    priority: 2
                }
            ]
        },
        {
            id: 'memory-usage-critical',
            name: 'Critical Memory Usage',
            description: 'Emergency rollback when memory usage exceeds 95%',
            enabled: true,
            conditions: {
                metricName: 'memory_usage_percentage',
                threshold: 95,
                operator: 'gt',
                duration: 30000, // 30 seconds
                consecutiveFailures: 2
            },
            actions: [
                {
                    type: 'emergency_disable',
                    target: 'all',
                    priority: 1
                },
                {
                    type: 'alert',
                    target: 'memory-critical',
                    priority: 2
                }
            ]
        }
    ];
    constructor() {
        this.metricsService = MetricsService.getInstance();
        this.featureFlagService = FeatureFlagService.getInstance();
        this.alertService = AlertService.getInstance();
        this.initializeDefaultTriggers();
        this.startMonitoring();
        console.log('🔄 RollbackService initialized with automated triggers');
    }
    static getInstance() {
        if (!RollbackService.instance) {
            RollbackService.instance = new RollbackService();
        }
        return RollbackService.instance;
    }
    /**
     * 🔄 ROLLBACK: Initialize default rollback triggers
     */
    initializeDefaultTriggers() {
        const now = Date.now();
        this.DEFAULT_TRIGGERS.forEach(trigger => {
            this.triggers.set(trigger.id, {
                ...trigger,
                metadata: {
                    createdAt: now,
                    triggerCount: 0
                }
            });
        });
        console.log(`🔄 Initialized ${this.triggers.size} rollback triggers`);
    }
    /**
     * 🔄 ROLLBACK: Start monitoring metrics for rollback conditions
     */
    startMonitoring() {
        // Check rollback conditions every 10 seconds
        this.monitoringInterval = setInterval(() => {
            this.checkRollbackConditions();
        }, 10000);
        console.log('🔄 Rollback monitoring started (10s interval)');
    }
    /**
     * 🔄 ROLLBACK: Check all rollback conditions
     */
    async checkRollbackConditions() {
        const enabledTriggers = Array.from(this.triggers.values()).filter(t => t.enabled);
        for (const trigger of enabledTriggers) {
            try {
                const shouldTrigger = await this.evaluateTriggerCondition(trigger);
                if (shouldTrigger) {
                    console.warn(`🔄 ROLLBACK TRIGGERED: ${trigger.name}`);
                    await this.executeRollback(trigger);
                }
            }
            catch (error) {
                console.error(`🔄 Error evaluating trigger ${trigger.name}:`, error);
            }
        }
    }
    /**
     * 🔄 ROLLBACK: Evaluate if a trigger condition is met
     */
    async evaluateTriggerCondition(trigger) {
        const { conditions } = trigger;
        // Get recent metrics for the specified metric
        const recentMetrics = this.metricsService.getMetrics('latency', 100)
            .filter(m => m.name === conditions.metricName)
            .sort((a, b) => b.timestamp - a.timestamp);
        if (recentMetrics.length === 0) {
            return false; // No data to evaluate
        }
        // Check if condition is met for the specified duration
        const now = Date.now();
        const cutoffTime = now - conditions.duration;
        const relevantMetrics = recentMetrics.filter(m => m.timestamp > cutoffTime);
        if (relevantMetrics.length === 0) {
            return false; // No recent data
        }
        // Check consecutive failures if specified
        if (conditions.consecutiveFailures) {
            const recentFailures = relevantMetrics.slice(0, conditions.consecutiveFailures);
            if (recentFailures.length < conditions.consecutiveFailures) {
                return false; // Not enough data points
            }
            // Check if all recent metrics meet the failure condition
            return recentFailures.every(metric => this.evaluateMetricCondition(metric.value, conditions.threshold, conditions.operator));
        }
        // Check if any metric in the duration meets the condition
        return relevantMetrics.some(metric => this.evaluateMetricCondition(metric.value, conditions.threshold, conditions.operator));
    }
    /**
     * 🔄 ROLLBACK: Evaluate metric against condition
     */
    evaluateMetricCondition(value, threshold, operator) {
        switch (operator) {
            case 'gt': return value > threshold;
            case 'lt': return value < threshold;
            case 'eq': return value === threshold;
            case 'gte': return value >= threshold;
            case 'lte': return value <= threshold;
            default: return false;
        }
    }
    /**
     * 🔄 ROLLBACK: Execute rollback actions
     */
    async executeRollback(trigger) {
        const executionId = `rollback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const startTime = performance.now();
        console.log(`🔄 Executing rollback: ${trigger.name} (${executionId})`);
        const execution = {
            id: executionId,
            triggerId: trigger.id,
            triggerName: trigger.name,
            timestamp: Date.now(),
            reason: `Trigger condition met: ${trigger.conditions.metricName} ${trigger.conditions.operator} ${trigger.conditions.threshold}`,
            actions: [],
            rollbackSuccess: false,
            totalExecutionTime: 0
        };
        // Sort actions by priority
        const sortedActions = [...trigger.actions].sort((a, b) => a.priority - b.priority);
        // Execute actions in priority order
        for (const action of sortedActions) {
            const actionStartTime = performance.now();
            try {
                await this.executeRollbackAction(action);
                execution.actions.push({
                    action,
                    success: true,
                    executedAt: Date.now()
                });
                console.log(`✅ Rollback action completed: ${action.type} ${action.target}`);
            }
            catch (error) {
                execution.actions.push({
                    action,
                    success: false,
                    error: error instanceof Error ? error.message : 'Unknown error',
                    executedAt: Date.now()
                });
                console.error(`❌ Rollback action failed: ${action.type} ${action.target}:`, error);
            }
        }
        // Update execution results
        execution.rollbackSuccess = execution.actions.every(a => a.success);
        execution.totalExecutionTime = Math.round(performance.now() - startTime);
        // Store execution history
        this.executions.unshift(execution);
        if (this.executions.length > this.MAX_EXECUTIONS_HISTORY) {
            this.executions = this.executions.slice(0, this.MAX_EXECUTIONS_HISTORY);
        }
        // Update trigger metadata
        const triggerToUpdate = this.triggers.get(trigger.id);
        if (triggerToUpdate) {
            triggerToUpdate.metadata.lastTriggered = Date.now();
            triggerToUpdate.metadata.triggerCount++;
        }
        // Record metrics
        this.metricsService.recordMetric('rollback_executed', 1, 'count', 'business');
        this.metricsService.recordMetric('rollback_success', execution.rollbackSuccess ? 1 : 0, 'boolean', 'business');
        this.metricsService.recordMetric('rollback_execution_time', execution.totalExecutionTime, 'ms', 'latency');
        console.log(`🔄 Rollback ${execution.rollbackSuccess ? 'completed successfully' : 'completed with errors'}: ${executionId} (${execution.totalExecutionTime}ms)`);
    }
    /**
     * 🔄 ROLLBACK: Execute individual rollback action
     */
    async executeRollbackAction(action) {
        switch (action.type) {
            case 'disable_feature':
                this.featureFlagService.updateFlag(action.target, { enabled: false });
                break;
            case 'enable_feature':
                this.featureFlagService.updateFlag(action.target, { enabled: true });
                break;
            case 'set_rollout':
                this.featureFlagService.updateFlag(action.target, { rolloutPercentage: action.value });
                break;
            case 'emergency_disable':
                if (action.target === 'all') {
                    this.featureFlagService.emergencyDisable();
                }
                else {
                    this.featureFlagService.updateFlag(action.target, { enabled: false, rolloutPercentage: 0 });
                }
                break;
            case 'alert':
                this.alertService.createAlert({
                    type: 'critical',
                    title: 'Automated Rollback Triggered',
                    message: `Rollback action executed: ${action.target}`,
                    source: 'rollback-service',
                    metadata: { action }
                });
                break;
            case 'custom':
                // Custom actions would be implemented here
                console.log(`🔄 Custom rollback action: ${action.target}`, action.value);
                break;
            default:
                throw new Error(`Unknown rollback action type: ${action.type}`);
        }
    }
    /**
     * 🔄 ROLLBACK: Add custom rollback trigger
     */
    addTrigger(trigger) {
        const now = Date.now();
        this.triggers.set(trigger.id, {
            ...trigger,
            metadata: {
                createdAt: now,
                triggerCount: 0
            }
        });
        console.log(`🔄 Added rollback trigger: ${trigger.name}`);
    }
    /**
     * 🔄 ROLLBACK: Update existing trigger
     */
    updateTrigger(triggerId, updates) {
        const trigger = this.triggers.get(triggerId);
        if (!trigger) {
            return false;
        }
        this.triggers.set(triggerId, {
            ...trigger,
            ...updates
        });
        console.log(`🔄 Updated rollback trigger: ${triggerId}`);
        return true;
    }
    /**
     * 🔄 ROLLBACK: Remove trigger
     */
    removeTrigger(triggerId) {
        const success = this.triggers.delete(triggerId);
        if (success) {
            console.log(`🔄 Removed rollback trigger: ${triggerId}`);
        }
        return success;
    }
    /**
     * 🔄 ROLLBACK: Get all triggers
     */
    getAllTriggers() {
        return Array.from(this.triggers.values());
    }
    /**
     * 🔄 ROLLBACK: Get execution history
     */
    getExecutionHistory(limit = 50) {
        return this.executions.slice(0, limit);
    }
    /**
     * 🔄 ROLLBACK: Get rollback statistics
     */
    getStatistics() {
        const triggers = Array.from(this.triggers.values());
        const enabledTriggers = triggers.filter(t => t.enabled);
        const successfulExecutions = this.executions.filter(e => e.rollbackSuccess);
        const mostTriggered = triggers.reduce((max, trigger) => trigger.metadata.triggerCount > (max?.metadata.triggerCount || 0) ? trigger : max, null);
        return {
            totalTriggers: triggers.length,
            enabledTriggers: enabledTriggers.length,
            totalExecutions: this.executions.length,
            successfulExecutions: successfulExecutions.length,
            lastExecution: this.executions[0] ? new Date(this.executions[0].timestamp) : undefined,
            mostTriggeredTrigger: mostTriggered?.name
        };
    }
    /**
     * 🔄 ROLLBACK: Manual rollback execution
     */
    async executeManualRollback(actions, reason) {
        const executionId = `manual_rollback_${Date.now()}`;
        const startTime = performance.now();
        console.log(`🔄 Executing manual rollback: ${reason} (${executionId})`);
        const execution = {
            id: executionId,
            triggerId: 'manual',
            triggerName: 'Manual Rollback',
            timestamp: Date.now(),
            reason,
            actions: [],
            rollbackSuccess: false,
            totalExecutionTime: 0
        };
        // Execute actions
        for (const action of actions) {
            try {
                await this.executeRollbackAction(action);
                execution.actions.push({
                    action,
                    success: true,
                    executedAt: Date.now()
                });
            }
            catch (error) {
                execution.actions.push({
                    action,
                    success: false,
                    error: error instanceof Error ? error.message : 'Unknown error',
                    executedAt: Date.now()
                });
            }
        }
        execution.rollbackSuccess = execution.actions.every(a => a.success);
        execution.totalExecutionTime = Math.round(performance.now() - startTime);
        // Store execution
        this.executions.unshift(execution);
        if (this.executions.length > this.MAX_EXECUTIONS_HISTORY) {
            this.executions = this.executions.slice(0, this.MAX_EXECUTIONS_HISTORY);
        }
        // Record metrics
        this.metricsService.recordMetric('manual_rollback_executed', 1, 'count', 'business');
        this.metricsService.recordMetric('manual_rollback_success', execution.rollbackSuccess ? 1 : 0, 'boolean', 'business');
        console.log(`🔄 Manual rollback ${execution.rollbackSuccess ? 'completed successfully' : 'completed with errors'}: ${executionId}`);
        return execution;
    }
    /**
     * 🔄 ROLLBACK: Cleanup resources
     */
    destroy() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        this.triggers.clear();
        this.executions = [];
        console.log('🔄 RollbackService destroyed');
    }
}
//# sourceMappingURL=RollbackService.js.map