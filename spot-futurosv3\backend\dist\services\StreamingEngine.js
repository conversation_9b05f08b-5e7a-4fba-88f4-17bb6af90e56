// 🚀 ULTRA-FAST Streaming Engine - Real-time opportunity processing and filtering
// Processes 1000+ opportunities per second with intelligent filtering
import { EventEmitter } from 'events';
import { WorkerManager } from '../workers/WorkerManager.js';
import { OpportunityBroadcaster } from './OpportunityBroadcaster.js';
import { UltraFastCache } from './UltraFastCache.js';
export class StreamingEngine extends EventEmitter {
    static instance;
    workerManager;
    broadcaster;
    cache;
    isRunning = false;
    processingInterval = null;
    broadcastInterval = null;
    // 🚀 PERFORMANCE: Streaming configuration
    config = {
        minSpread: 0.3, // Minimum spread percentage to broadcast
        maxSpread: 50, // Maximum spread percentage (filter outliers)
        minVolume: 1000, // Minimum volume in USD
        maxDataAge: 10000, // Maximum data age in milliseconds
        batchSize: 100, // Process opportunities in batches
        processingInterval: 500, // Process every 500ms
        broadcastThrottle: 1000 // Broadcast every 1 second
    };
    // 🚀 PERFORMANCE: Metrics tracking
    metrics = {
        opportunitiesProcessed: 0,
        opportunitiesFiltered: 0,
        opportunitiesBroadcast: 0,
        averageProcessingTime: 0,
        filterEfficiency: 0,
        throughput: 0,
        lastProcessedAt: 0
    };
    // 🚀 PERFORMANCE: Processing queues
    processingQueue = [];
    broadcastQueue = [];
    lastBroadcast = 0;
    // 🚀 PERFORMANCE: Filter cache for efficiency
    filterCache = new Map();
    lastOpportunities = [];
    constructor() {
        super();
        this.workerManager = WorkerManager.getInstance();
        this.broadcaster = new OpportunityBroadcaster();
        this.cache = UltraFastCache.getInstance();
    }
    static getInstance() {
        if (!StreamingEngine.instance) {
            StreamingEngine.instance = new StreamingEngine();
        }
        return StreamingEngine.instance;
    }
    /**
     * 🚀 START: Initialize streaming engine
     */
    async start() {
        if (this.isRunning)
            return;
        console.log('🔥 StreamingEngine: Starting...');
        this.isRunning = true;
        // Initialize broadcaster (it doesn't have a start method)
        // The broadcaster is initialized when the server starts
        // Start processing loop
        this.processingInterval = setInterval(() => {
            this.processOpportunities();
        }, this.config.processingInterval);
        // Start broadcast loop
        this.broadcastInterval = setInterval(() => {
            this.broadcastOpportunities();
        }, this.config.broadcastThrottle);
        console.log('✅ StreamingEngine: Started successfully');
        this.emit('started');
    }
    /**
     * 🛑 STOP: Stop streaming engine
     */
    async stop() {
        if (!this.isRunning)
            return;
        console.log('🛑 StreamingEngine: Stopping...');
        this.isRunning = false;
        // Stop intervals
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = null;
        }
        if (this.broadcastInterval) {
            clearInterval(this.broadcastInterval);
            this.broadcastInterval = null;
        }
        // Cleanup broadcaster
        this.broadcaster.cleanup();
        console.log('✅ StreamingEngine: Stopped');
        this.emit('stopped');
    }
    /**
     * 🚀 PROCESS: Main processing loop
     */
    async processOpportunities() {
        const startTime = performance.now();
        try {
            // Get fresh opportunities from workers
            const opportunities = this.workerManager.getOpportunities();
            if (opportunities.length === 0)
                return;
            // 🚀 ULTRA-FAST: Detect changes using quick comparison
            const hasChanges = this.detectChanges(opportunities);
            if (!hasChanges) {
                // No changes, skip processing but update metrics
                this.updateThroughputMetrics(opportunities.length, 0);
                return;
            }
            // 🚀 FILTER: Apply intelligent filtering
            const filteredOpportunities = this.applyIntelligentFilters(opportunities);
            // 🚀 CACHE: Store filtered opportunities
            this.cache.set('streaming_opportunities', filteredOpportunities, 'hot');
            // 🚀 QUEUE: Add to broadcast queue
            this.broadcastQueue = filteredOpportunities;
            // Update metrics
            const processingTime = performance.now() - startTime;
            this.updateProcessingMetrics(opportunities.length, filteredOpportunities.length, processingTime);
            // Store for next comparison
            this.lastOpportunities = opportunities;
            this.emit('processed', {
                total: opportunities.length,
                filtered: filteredOpportunities.length,
                processingTime
            });
        }
        catch (error) {
            console.error('❌ StreamingEngine: Processing error:', error);
            this.emit('error', error);
        }
    }
    /**
     * 🚀 DETECT: Quick change detection
     */
    detectChanges(opportunities) {
        // Quick length check
        if (opportunities.length !== this.lastOpportunities.length) {
            return true;
        }
        // Quick sample check (first 10 items)
        const sampleSize = Math.min(10, opportunities.length);
        for (let i = 0; i < sampleSize; i++) {
            const current = opportunities[i];
            const previous = this.lastOpportunities[i];
            if (!previous ||
                current.symbol !== previous.symbol ||
                Math.abs(current.spreadPercentage - previous.spreadPercentage) > 0.01 ||
                current.timestamp !== previous.timestamp) {
                return true;
            }
        }
        return false;
    }
    /**
     * 🚀 FILTER: Apply intelligent filtering with caching
     */
    applyIntelligentFilters(opportunities) {
        const filtered = [];
        const now = Date.now();
        for (const opportunity of opportunities) {
            // Create cache key for this opportunity
            const cacheKey = `${opportunity.symbol}_${opportunity.spreadPercentage.toFixed(3)}_${opportunity.volume}`;
            // Check filter cache first
            if (this.filterCache.has(cacheKey)) {
                if (this.filterCache.get(cacheKey)) {
                    filtered.push(opportunity);
                }
                continue;
            }
            // Apply filters
            const passesFilter = this.passesFilters(opportunity, now);
            // Cache result
            this.filterCache.set(cacheKey, passesFilter);
            // Clean cache if it gets too large
            if (this.filterCache.size > 1000) {
                const firstKey = this.filterCache.keys().next().value;
                this.filterCache.delete(firstKey);
            }
            if (passesFilter) {
                filtered.push(opportunity);
            }
        }
        return filtered.sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage));
    }
    /**
     * 🚀 FILTER: Check if opportunity passes filters
     */
    passesFilters(opportunity, now) {
        const spread = Math.abs(opportunity.spreadPercentage);
        const dataAge = now - (opportunity.timestamp || 0);
        // Basic filters
        if (spread < this.config.minSpread || spread > this.config.maxSpread) {
            return false;
        }
        if ((opportunity.volume || 0) < this.config.minVolume) {
            return false;
        }
        if (dataAge > this.config.maxDataAge) {
            return false;
        }
        // Quality filters
        if (opportunity.spotPrice <= 0 || opportunity.futuresPrice <= 0) {
            return false;
        }
        return true;
    }
    /**
     * 🚀 BROADCAST: Send opportunities to connected clients
     */
    async broadcastOpportunities() {
        if (this.broadcastQueue.length === 0)
            return;
        const now = Date.now();
        // Throttle broadcasts
        if (now - this.lastBroadcast < this.config.broadcastThrottle) {
            return;
        }
        try {
            // ✅ SEM LIMITE: Broadcast todas as oportunidades na fila
            const topOpportunities = this.broadcastQueue;
            // Broadcast to all connected clients
            await this.broadcaster.broadcastOpportunities(topOpportunities);
            // Update metrics
            this.metrics.opportunitiesBroadcast += topOpportunities.length;
            this.lastBroadcast = now;
            this.emit('broadcast', {
                count: topOpportunities.length,
                timestamp: now
            });
        }
        catch (error) {
            console.error('❌ StreamingEngine: Broadcast error:', error);
            this.emit('error', error);
        }
    }
    /**
     * 📊 METRICS: Update processing metrics
     */
    updateProcessingMetrics(total, filtered, processingTime) {
        this.metrics.opportunitiesProcessed += total;
        this.metrics.opportunitiesFiltered += filtered;
        this.metrics.lastProcessedAt = Date.now();
        // Update average processing time
        const currentAvg = this.metrics.averageProcessingTime;
        this.metrics.averageProcessingTime = currentAvg === 0
            ? processingTime
            : (currentAvg * 0.9 + processingTime * 0.1);
        // Update filter efficiency
        this.metrics.filterEfficiency = total > 0 ? (filtered / total) * 100 : 0;
    }
    /**
     * 📊 METRICS: Update throughput metrics
     */
    updateThroughputMetrics(processed, timeMs) {
        const throughput = timeMs > 0 ? (processed / timeMs) * 1000 : 0;
        this.metrics.throughput = this.metrics.throughput === 0
            ? throughput
            : (this.metrics.throughput * 0.9 + throughput * 0.1);
    }
    /**
     * ⚙️ CONFIG: Update streaming configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        // Clear filter cache when config changes
        this.filterCache.clear();
        console.log('⚙️ StreamingEngine: Configuration updated', this.config);
        this.emit('configUpdated', this.config);
    }
    /**
     * 📊 GET: Get streaming metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            config: this.config,
            isRunning: this.isRunning
        };
    }
    /**
     * 📊 GET: Get broadcaster stats
     */
    getBroadcasterStats() {
        return this.broadcaster.getStats();
    }
    /**
     * 🔍 GET: Get current filtered opportunities
     */
    getCurrentOpportunities() {
        return this.cache.get('streaming_opportunities') || [];
    }
}
//# sourceMappingURL=StreamingEngine.js.map