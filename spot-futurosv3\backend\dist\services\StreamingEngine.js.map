{"version": 3, "file": "StreamingEngine.js", "sourceRoot": "", "sources": ["../../src/services/StreamingEngine.ts"], "names": [], "mappings": "AAAA,kFAAkF;AAClF,sEAAsE;AAEtE,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AACrE,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAgCrD,MAAM,OAAO,eAAgB,SAAQ,YAAY;IACvC,MAAM,CAAC,QAAQ,CAAkB;IACjC,aAAa,CAAgB;IAC7B,WAAW,CAAyB;IACpC,KAAK,CAAiB;IAEtB,SAAS,GAAY,KAAK,CAAC;IAC3B,kBAAkB,GAA0B,IAAI,CAAC;IACjD,iBAAiB,GAA0B,IAAI,CAAC;IAExD,0CAA0C;IAClC,MAAM,GAAoB;QAChC,SAAS,EAAE,GAAG,EAAS,yCAAyC;QAChE,SAAS,EAAE,EAAE,EAAU,8CAA8C;QACrE,SAAS,EAAE,IAAI,EAAQ,wBAAwB;QAC/C,UAAU,EAAE,KAAK,EAAM,mCAAmC;QAC1D,SAAS,EAAE,GAAG,EAAS,mCAAmC;QAC1D,kBAAkB,EAAE,GAAG,EAAE,sBAAsB;QAC/C,iBAAiB,EAAE,IAAI,CAAE,2BAA2B;KACrD,CAAC;IAEF,mCAAmC;IAC3B,OAAO,GAAqB;QAClC,sBAAsB,EAAE,CAAC;QACzB,qBAAqB,EAAE,CAAC;QACxB,sBAAsB,EAAE,CAAC;QACzB,qBAAqB,EAAE,CAAC;QACxB,gBAAgB,EAAE,CAAC;QACnB,UAAU,EAAE,CAAC;QACb,eAAe,EAAE,CAAC;KACnB,CAAC;IAEF,oCAAoC;IAC5B,eAAe,GAA2B,EAAE,CAAC;IAC7C,cAAc,GAA2B,EAAE,CAAC;IAC5C,aAAa,GAAW,CAAC,CAAC;IAElC,8CAA8C;IACtC,WAAW,GAAG,IAAI,GAAG,EAAmB,CAAC;IACzC,iBAAiB,GAA2B,EAAE,CAAC;IAEvD;QACE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAChD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;IAC5C,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,0DAA0D;QAC1D,wDAAwD;QAExD,wBAAwB;QACxB,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,GAAG,EAAE;YACzC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAEnC,uBAAuB;QACvB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAElC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,iBAAiB;QACjB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAChC,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAE3B,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB;QAChC,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,uCAAuC;YACvC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;YAE5D,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO;YAEvC,uDAAuD;YACvD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YAErD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,iDAAiD;gBACjD,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBACtD,OAAO;YACT,CAAC;YAED,yCAAyC;YACzC,MAAM,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;YAE1E,yCAAyC;YACzC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,yBAAyB,EAAE,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAExE,mCAAmC;YACnC,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC;YAE5C,iBAAiB;YACjB,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACrD,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,MAAM,EAAE,qBAAqB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAEjG,4BAA4B;YAC5B,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC;YAEvC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,KAAK,EAAE,aAAa,CAAC,MAAM;gBAC3B,QAAQ,EAAE,qBAAqB,CAAC,MAAM;gBACtC,cAAc;aACf,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,aAAqC;QACzD,qBAAqB;QACrB,IAAI,aAAa,CAAC,MAAM,KAAK,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,sCAAsC;QACtC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;QACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAE3C,IAAI,CAAC,QAAQ;gBACT,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM;gBAClC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,GAAG,IAAI;gBACrE,OAAO,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC7C,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,aAAqC;QACnE,MAAM,QAAQ,GAA2B,EAAE,CAAC;QAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,WAAW,IAAI,aAAa,EAAE,CAAC;YACxC,wCAAwC;YACxC,MAAM,QAAQ,GAAG,GAAG,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;YAE1G,2BAA2B;YAC3B,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACnC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC7B,CAAC;gBACD,SAAS;YACX,CAAC;YAED,gBAAgB;YAChB,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;YAE1D,eAAe;YACf,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAE7C,mCAAmC;YACnC,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC;gBACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;gBACtD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;YAED,IAAI,YAAY,EAAE,CAAC;gBACjB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,WAAiC,EAAE,GAAW;QAClE,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;QACtD,MAAM,OAAO,GAAG,GAAG,GAAG,CAAC,WAAW,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;QAEnD,gBAAgB;QAChB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACrE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,kBAAkB;QAClB,IAAI,WAAW,CAAC,SAAS,IAAI,CAAC,IAAI,WAAW,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAClC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE7C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,sBAAsB;QACtB,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC7D,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,yDAAyD;YACzD,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC;YAE7C,qCAAqC;YACrC,MAAM,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;YAEhE,iBAAiB;YACjB,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,gBAAgB,CAAC,MAAM,CAAC;YAC/D,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;YAEzB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,KAAK,EAAE,gBAAgB,CAAC,MAAM;gBAC9B,SAAS,EAAE,GAAG;aACf,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,KAAa,EAAE,QAAgB,EAAE,cAAsB;QACrF,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,KAAK,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,qBAAqB,IAAI,QAAQ,CAAC;QAC/C,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE1C,iCAAiC;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,qBAAqB,GAAG,UAAU,KAAK,CAAC;YACnD,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,CAAC,UAAU,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,CAAC,CAAC;QAE9C,2BAA2B;QAC3B,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,SAAiB,EAAE,MAAc;QAC/D,MAAM,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC;YACrD,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,GAAG,GAAG,UAAU,GAAG,GAAG,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAmC;QAC9C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;QAE/C,yCAAyC;QACzC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAEzB,OAAO,CAAC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,GAAG,IAAI,CAAC,OAAO;YACf,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,EAAE,CAAC;IACzD,CAAC;CACF"}