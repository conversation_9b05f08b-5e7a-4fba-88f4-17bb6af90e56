{"version": 3, "file": "UltraFastCache.js", "sourceRoot": "", "sources": ["../../src/services/UltraFastCache.ts"], "names": [], "mappings": "AAAA,yCAAyC;AACzC,gDAAgD;AAChD,mDAAmD;AACnD,sDAAsD;AAqBtD,MAAM,OAAO,cAAc;IACjB,MAAM,CAAC,QAAQ,CAAiB;IAExC,6CAA6C;IACrC,QAAQ,GAAG,IAAI,GAAG,EAAe,CAAC;IACzB,eAAe,GAAG,GAAG,CAAC;IACtB,OAAO,GAAG,IAAI,CAAC,CAAC,YAAY;IAE7C,yCAAyC;IACjC,SAAS,GAAG,IAAI,GAAG,EAA2B,CAAC;IACtC,gBAAgB,GAAG,GAAG,CAAC;IACvB,QAAQ,GAAG,KAAK,CAAC,CAAC,aAAa;IAEhD,yCAAyC;IACjC,SAAS,GAAG,IAAI,GAAG,EAA2B,CAAC;IACtC,gBAAgB,GAAG,IAAI,CAAC;IACxB,QAAQ,GAAG,MAAM,CAAC,CAAC,YAAY;IAExC,OAAO,GAAiB;QAC9B,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,CAAC;QACT,UAAU,EAAE,CAAC;QACb,SAAS,EAAE,CAAC;QACZ,aAAa,EAAE,CAAC;KACjB,CAAC;IAEF;QACE,gCAAgC;QAChC,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,cAAc,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,GAAG,CAAI,GAAW;QAChB,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,uCAAuC;QACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChC,CAAC;QAED,0CAA0C;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,SAAS,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;YAC7D,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtB,SAAS,CAAC,WAAW,EAAE,CAAC;YACxB,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC;YAE3B,uDAAuD;YACvD,IAAI,SAAS,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;YACzC,CAAC;YAED,OAAO,SAAS,CAAC,IAAI,CAAC;QACxB,CAAC;QAED,wCAAwC;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,SAAS,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;YAC7D,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtB,SAAS,CAAC,WAAW,EAAE,CAAC;YACxB,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC;YAE3B,6CAA6C;YAC7C,IAAI,SAAS,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;gBACxC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC7B,CAAC;YAED,OAAO,SAAS,CAAC,IAAI,CAAC;QACxB,CAAC;QAED,iCAAiC;QACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,GAAG,CAAI,GAAW,EAAE,IAAO,EAAE,WAAoC,MAAM;QACrE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,KAAK;gBACR,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBACvB,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC7B,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC7B,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAI,GAAW,EAAE,IAAO;QACpC,uBAAuB;QACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YACnD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAE7B,6BAA6B;QAC7B,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,OAAO,CAAI,GAAW,EAAE,IAAO,EAAE,SAAiB;QACxD,uBAAuB;QACvB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC3B,CAAC;QAED,MAAM,KAAK,GAAkB;YAC3B,IAAI;YACJ,SAAS;YACT,GAAG,EAAE,IAAI,CAAC,QAAQ;YAClB,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,SAAS;YACrB,KAAK,EAAE,MAAM;SACd,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,OAAO,CAAI,GAAW,EAAE,IAAO,EAAE,SAAiB;QACxD,uBAAuB;QACvB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC3B,CAAC;QAED,MAAM,KAAK,GAAkB;YAC3B,IAAI;YACJ,SAAS;YACT,GAAG,EAAE,IAAI,CAAC,QAAQ;YAClB,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,SAAS;YACrB,KAAK,EAAE,MAAM;SACd,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,YAAY,CAAI,GAAW,EAAE,IAAO;QAC1C,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,aAAa,CAAI,GAAW,EAAE,IAAO;QAC3C,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACpC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,OAAO;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,mBAAmB;QACnB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YACpD,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;gBACtC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC3B,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YACpD,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;gBACtC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC3B,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,0BAA0B,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAClF,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC;YAC5C,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG;YAChD,CAAC,CAAC,CAAC,CAAC;QAEN,OAAO;YACL,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACvC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;YACzC,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBACvB,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBACvB,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBACvB,KAAK,EAAE,SAAS;aACjB;YACD,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAC3B,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;YACnC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;YACjC,KAAK,EAAE;gBACL,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;gBACvB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;gBACzB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;gBACzB,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;aACtE;YACD,MAAM,EAAE;gBACN,GAAG,EAAE,IAAI,CAAC,eAAe;gBACzB,IAAI,EAAE,IAAI,CAAC,gBAAgB;gBAC3B,IAAI,EAAE,IAAI,CAAC,gBAAgB;aAC5B;YACD,WAAW,EAAE;gBACX,SAAS,EAAE,MAAM;gBACjB,SAAS,EAAE,OAAO;gBAClB,SAAS,EAAE,OAAO;aACnB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QAEvB,gBAAgB;QAChB,IAAI,CAAC,OAAO,GAAG;YACb,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;YACT,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,CAAC;YACZ,aAAa,EAAE,CAAC;SACjB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,GAAG,CAAC,GAAW;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC;YACvB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1C,OAAO,OAAO,CAAC;IACjB,CAAC;CACF"}