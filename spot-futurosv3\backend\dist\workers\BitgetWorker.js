// 🚀 ULTRA-FAST Bitget Worker - Dedicated processing for maximum performance
// Target: <1s response time with intelligent caching
import axios from 'axios';
import https from 'https';
import { CacheService } from '../services/CacheService.js';
export class BitgetWorker {
    client;
    cache;
    isRunning = false;
    lastUpdate = 0;
    updateInterval = null;
    // 🚀 PERFORMANCE: Shared data to avoid repeated processing
    cachedSpotData = [];
    cachedFuturesData = [];
    constructor() {
        this.cache = CacheService.getInstance();
        this.initializeClient();
    }
    /**
     * 🚀 ULTRA-FAST: Initialize HTTP client with aggressive optimization
     */
    initializeClient() {
        const httpsAgent = new https.Agent({
            keepAlive: true,
            maxSockets: 25, // Increased for better concurrency
            maxFreeSockets: 10, // More free sockets
            timeout: 600, // Faster timeout
            freeSocketTimeout: 45000 // Longer keep-alive
        });
        this.client = axios.create({
            baseURL: 'https://api.bitget.com',
            timeout: 3000,
            httpsAgent,
            headers: {
                'User-Agent': 'Ultra-Fast-Arbitrage-Bot/1.0',
                'Accept': 'application/json',
                'Connection': 'keep-alive'
            }
        });
    }
    /**
     * 🚀 MAIN: Start continuous data fetching
     */
    async start() {
        if (this.isRunning)
            return;
        this.isRunning = true;
        console.log('🔥 Bitget Worker: Started');
        // Initial fetch
        await this.fetchData();
        // Continuous updates every 1.5 seconds (Bitget is fast)
        this.updateInterval = setInterval(async () => {
            await this.fetchData();
        }, 1500);
    }
    /**
     * 🛑 Stop worker
     */
    stop() {
        this.isRunning = false;
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        console.log('🛑 Bitget Worker: Stopped');
    }
    /**
     * 🚀 ULTRA-FAST: Fetch data with parallel processing
     */
    async fetchData() {
        const startTime = performance.now();
        try {
            // 🔥 PARALLEL: Fetch spot and futures simultaneously
            const [spotResult, futuresResult] = await Promise.allSettled([
                this.fetchSpotData(),
                this.fetchFuturesData()
            ]);
            let spotCount = 0;
            let futuresCount = 0;
            // Process spot data
            if (spotResult.status === 'fulfilled') {
                this.cachedSpotData = spotResult.value;
                spotCount = spotResult.value.length;
            }
            else {
                console.error('❌ Bitget Worker: Spot data failed:', spotResult.reason?.message);
            }
            // Process futures data
            if (futuresResult.status === 'fulfilled') {
                this.cachedFuturesData = futuresResult.value;
                futuresCount = futuresResult.value.length;
            }
            else {
                console.error('❌ Bitget Worker: Futures data failed:', futuresResult.reason?.message);
            }
            const processingTime = performance.now() - startTime;
            this.lastUpdate = Date.now();
            // 🚀 CACHE: Store processed data
            const exchangeData = {
                exchange: 'bitget',
                spot: this.cachedSpotData,
                futures: this.cachedFuturesData,
                timestamp: this.lastUpdate,
                status: 'success'
            };
            this.cache.setUltraFast('bitget_worker_data', exchangeData, true); // Hot cache
            console.log(`⚡ Bitget Worker: ${spotCount + futuresCount} pairs in ${processingTime.toFixed(1)}ms`);
        }
        catch (error) {
            console.error('❌ Bitget Worker: Critical error:', error);
        }
    }
    /**
     * 🚀 SPOT: Fetch spot data
     */
    async fetchSpotData() {
        const response = await this.client.get('/api/spot/v1/market/tickers');
        // Bitget returns data in response.data.data format
        const responseData = response.data?.data || response.data;
        const tickers = Array.isArray(responseData) ? responseData : [];
        // 🚀 PARALLEL: Process tickers in larger batches for better performance
        return this.processInParallel(tickers, (ticker) => this.normalizeSpotData(ticker), 400);
    }
    /**
     * 🚀 FUTURES: Fetch futures data
     */
    async fetchFuturesData() {
        const response = await this.client.get('/api/mix/v1/market/tickers?productType=UMCBL');
        // Bitget returns data in response.data.data format
        const responseData = response.data?.data || response.data;
        const tickers = Array.isArray(responseData) ? responseData : [];
        // 🚀 PARALLEL: Process tickers in larger batches for better performance
        return this.processInParallel(tickers, (ticker) => this.normalizeFuturesData(ticker), 350);
    }
    /**
     * 🚀 PARALLEL: Process array in parallel batches
     */
    async processInParallel(items, processor, batchSize = 100) {
        const results = [];
        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            const batchResults = batch
                .map(processor)
                .filter((result) => result !== null);
            results.push(...batchResults);
        }
        return results;
    }
    /**
     * 🔄 NORMALIZE: Convert Bitget spot data to standard format
     */
    normalizeSpotData(ticker) {
        try {
            const price = parseFloat(ticker.close) || 0;
            if (price <= 0)
                return null;
            let symbol = ticker.symbol || '';
            // 🔄 NORMALIZE: Convert Bitget symbol format to standard
            if (symbol.endsWith('USDT') && !symbol.includes('/')) {
                symbol = symbol.replace('USDT', '/USDT');
            }
            else if (symbol.endsWith('BTC') && !symbol.includes('/')) {
                symbol = symbol.replace('BTC', '/BTC');
            }
            else if (symbol.endsWith('ETH') && !symbol.includes('/')) {
                symbol = symbol.replace('ETH', '/ETH');
            }
            return {
                symbol,
                price,
                bid: parseFloat(ticker.bidPr) || price * 0.999,
                ask: parseFloat(ticker.askPr) || price * 1.001,
                volume: parseFloat(ticker.baseVol) || 0,
                change24h: parseFloat(ticker.change) || 0,
                high24h: parseFloat(ticker.high24h) || price,
                low24h: parseFloat(ticker.low24h) || price,
                timestamp: Date.now()
            };
        }
        catch {
            return null;
        }
    }
    /**
     * 🔄 NORMALIZE: Convert Bitget futures data to standard format
     */
    normalizeFuturesData(ticker) {
        try {
            const price = parseFloat(ticker.last) || 0;
            if (price <= 0)
                return null;
            return {
                symbol: ticker.symbol || '',
                price,
                bid: parseFloat(ticker.bestBid) || 0,
                ask: parseFloat(ticker.bestAsk) || 0,
                volume: parseFloat(ticker.baseVolume) || 0,
                change24h: parseFloat(ticker.chgUtc) || 0,
                high24h: parseFloat(ticker.high24h) || price,
                low24h: parseFloat(ticker.low24h) || price,
                openInterest: parseFloat(ticker.openInterest) || 0,
                fundingRate: parseFloat(ticker.fundingRate) || 0,
                timestamp: Date.now()
            };
        }
        catch {
            return null;
        }
    }
    /**
     * 📊 GET: Retrieve cached data
     */
    getData() {
        return this.cache.getUltraFast('bitget_worker_data');
    }
    /**
     * 📊 STATUS: Get worker status
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            lastUpdate: this.lastUpdate,
            spotPairs: this.cachedSpotData.length,
            futuresPairs: this.cachedFuturesData.length,
            totalPairs: this.cachedSpotData.length + this.cachedFuturesData.length
        };
    }
}
//# sourceMappingURL=BitgetWorker.js.map