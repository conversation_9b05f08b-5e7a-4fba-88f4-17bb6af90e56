{"version": 3, "file": "BitgetWorker.js", "sourceRoot": "", "sources": ["../../src/workers/BitgetWorker.ts"], "names": [], "mappings": "AAAA,6EAA6E;AAC7E,qDAAqD;AAErD,OAAO,KAAwB,MAAM,OAAO,CAAC;AAC7C,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAE3D,MAAM,OAAO,YAAY;IACf,MAAM,CAAgB;IACtB,KAAK,CAAe;IACpB,SAAS,GAAY,KAAK,CAAC;IAC3B,UAAU,GAAW,CAAC,CAAC;IACvB,cAAc,GAA0B,IAAI,CAAC;IAErD,2DAA2D;IACnD,cAAc,GAAe,EAAE,CAAC;IAChC,iBAAiB,GAAkB,EAAE,CAAC;IAE9C;QACE,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;QACxC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,UAAU,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC;YACjC,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,EAAE,EAAE,mCAAmC;YACnD,cAAc,EAAE,EAAE,EAAE,oBAAoB;YACxC,OAAO,EAAE,GAAG,EAAE,iBAAiB;YAC/B,iBAAiB,EAAE,KAAK,CAAC,oBAAoB;SAC9C,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,wBAAwB;YACjC,OAAO,EAAE,IAAI;YACb,UAAU;YACV,OAAO,EAAE;gBACP,YAAY,EAAE,8BAA8B;gBAC5C,QAAQ,EAAE,kBAAkB;gBAC5B,YAAY,EAAE,YAAY;aAC3B;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAEzC,gBAAgB;QAChB,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QAEvB,wDAAwD;QACxD,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC3C,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACH,IAAI;QACF,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,SAAS;QACrB,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,qDAAqD;YACrD,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;gBAC3D,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,gBAAgB,EAAE;aACxB,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,oBAAoB;YACpB,IAAI,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBACtC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;gBACvC,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAClF,CAAC;YAED,uBAAuB;YACvB,IAAI,aAAa,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBACzC,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,KAAK,CAAC;gBAC7C,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACxF,CAAC;YAED,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACrD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,iCAAiC;YACjC,MAAM,YAAY,GAAiB;gBACjC,QAAQ,EAAE,QAAQ;gBAClB,IAAI,EAAE,IAAI,CAAC,cAAc;gBACzB,OAAO,EAAE,IAAI,CAAC,iBAAiB;gBAC/B,SAAS,EAAE,IAAI,CAAC,UAAU;gBAC1B,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,oBAAoB,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,YAAY;YAE/E,OAAO,CAAC,GAAG,CAAC,oBAAoB,SAAS,GAAG,YAAY,aAAa,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAEtG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa;QACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAEtE,mDAAmD;QACnD,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC;QAC1D,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;QAEhE,wEAAwE;QACxE,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC5B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAEvF,mDAAmD;QACnD,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC;QAC1D,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;QAEhE,wEAAwE;QACxE,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;IAC7F,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,KAAU,EACV,SAAgC,EAChC,YAAoB,GAAG;QAEvB,MAAM,OAAO,GAAQ,EAAE,CAAC;QAExB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACjD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;YAC5C,MAAM,YAAY,GAAG,KAAK;iBACvB,GAAG,CAAC,SAAS,CAAC;iBACd,MAAM,CAAC,CAAC,MAAM,EAAe,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC;YAEpD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAW;QACnC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,KAAK,IAAI,CAAC;gBAAE,OAAO,IAAI,CAAC;YAE5B,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;YAEjC,yDAAyD;YACzD,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACrD,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC3C,CAAC;iBAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3D,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACzC,CAAC;iBAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3D,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACzC,CAAC;YAED,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,KAAK;gBAC9C,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,KAAK;gBAC9C,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;gBACvC,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;gBACzC,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK;gBAC5C,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK;gBAC1C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,MAAW;QACtC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,KAAK,IAAI,CAAC;gBAAE,OAAO,IAAI,CAAC;YAE5B,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,EAAE;gBAC3B,KAAK;gBACL,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;gBACpC,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;gBACpC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;gBAC1C,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;gBACzC,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK;gBAC5C,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK;gBAC1C,YAAY,EAAE,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;gBAClD,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;gBAChD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;YACrC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM;YAC3C,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM;SACvE,CAAC;IACJ,CAAC;CACF"}