// 🚀 ULTRA-FAST Gate.io Worker - Dedicated processing for maximum performance
// Target: <2s response time with intelligent caching
import axios from 'axios';
import https from 'https';
import crypto from 'crypto';
import { CacheService } from '../services/CacheService.js';
export class GateioWorker {
    client;
    cache;
    isRunning = false;
    lastUpdate = 0;
    updateInterval = null;
    // 🚀 PERFORMANCE: Shared data to avoid repeated processing
    cachedSpotData = [];
    cachedFuturesData = [];
    constructor() {
        this.cache = CacheService.getInstance();
        this.initializeClient();
    }
    /**
     * 🚀 ULTRA-FAST: Initialize HTTP client with aggressive optimization
     */
    initializeClient() {
        const httpsAgent = new https.Agent({
            keepAlive: true,
            maxSockets: 35, // Highest for Gate.io (largest dataset)
            maxFreeSockets: 20, // Many free sockets
            timeout: 800, // Faster timeout after removing HMAC
            freeSocketTimeout: 90000 // Very long keep-alive
        });
        this.client = axios.create({
            baseURL: process.env.GATEIO_API_URL || 'https://api.gateio.ws',
            timeout: 5000,
            httpsAgent,
            headers: {
                'User-Agent': 'Ultra-Fast-Arbitrage-Bot/1.0',
                'Accept': 'application/json',
                'Connection': 'keep-alive'
            }
        });
    }
    /**
     * 🚀 MAIN: Start continuous data fetching
     */
    async start() {
        if (this.isRunning)
            return;
        this.isRunning = true;
        console.log('🔥 Gate.io Worker: Started');
        // Initial fetch
        await this.fetchData();
        // Continuous updates every 5 seconds (Gate.io optimization)
        this.updateInterval = setInterval(async () => {
            await this.fetchData();
        }, 5000);
    }
    /**
     * 🛑 Stop worker
     */
    stop() {
        this.isRunning = false;
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        console.log('🛑 Gate.io Worker: Stopped');
    }
    /**
     * 🚀 ULTRA-FAST: Fetch data with parallel processing
     */
    async fetchData() {
        const startTime = performance.now();
        try {
            // 🔥 PARALLEL: Fetch spot and futures simultaneously
            const [spotResult, futuresResult] = await Promise.allSettled([
                this.fetchSpotData(),
                this.fetchFuturesData()
            ]);
            let spotCount = 0;
            let futuresCount = 0;
            // Process spot data
            if (spotResult.status === 'fulfilled') {
                this.cachedSpotData = spotResult.value;
                spotCount = spotResult.value.length;
            }
            else {
                console.error('❌ Gate.io Worker: Spot data failed:', spotResult.reason?.message);
            }
            // Process futures data
            if (futuresResult.status === 'fulfilled') {
                this.cachedFuturesData = futuresResult.value;
                futuresCount = futuresResult.value.length;
            }
            else {
                console.error('❌ Gate.io Worker: Futures data failed:', futuresResult.reason?.message);
            }
            const processingTime = performance.now() - startTime;
            this.lastUpdate = Date.now();
            // 🚀 CACHE: Store processed data
            const exchangeData = {
                exchange: 'gateio',
                spot: this.cachedSpotData,
                futures: this.cachedFuturesData,
                timestamp: this.lastUpdate,
                status: 'success'
            };
            this.cache.setUltraFast('gateio_worker_data', exchangeData, true); // Hot cache
            console.log(`⚡ Gate.io Worker: ${spotCount + futuresCount} pairs in ${processingTime.toFixed(1)}ms`);
        }
        catch (error) {
            console.error('❌ Gate.io Worker: Critical error:', error);
        }
    }
    /**
     * 🚀 SPOT: Fetch spot data with ultra-fast optimization
     */
    async fetchSpotData() {
        try {
            // 🚀 ULTRA-FAST: Use fastest endpoint with longer timeout
            const response = await Promise.race([
                this.client.get('/api/v4/spot/tickers'),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
            ]);
            const tickers = Array.isArray(response.data) ? response.data : [];
            // 🚀 ULTRA-PARALLEL: Process in massive batches
            const result = await this.processInParallel(tickers, (ticker) => this.normalizeSpotData(ticker), 1000);
            // Cache successful data for fallback
            if (result.length > 0) {
                this.cache.setUltraFast('gateio_spot_fallback', result, true);
            }
            return result;
        }
        catch (error) {
            console.warn('⚠️ Gate.io spot fetch failed, using cached data');
            // Return cached data if available
            const cached = this.cache.getUltraFast('gateio_spot_fallback');
            return cached || [];
        }
    }
    /**
     * 🚀 FUTURES: Fetch futures data with ultra-fast optimization
     */
    async fetchFuturesData() {
        try {
            // 🚀 ULTRA-FAST: Use fastest endpoint with longer timeout
            const response = await Promise.race([
                this.client.get('/api/v4/futures/usdt/tickers'),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
            ]);
            const tickers = Array.isArray(response.data) ? response.data : [];
            // 🚀 ULTRA-PARALLEL: Process in massive batches
            const result = await this.processInParallel(tickers, (ticker) => this.normalizeFuturesData(ticker), 800);
            // Cache successful data for fallback
            if (result.length > 0) {
                this.cache.setUltraFast('gateio_futures_fallback', result, true);
            }
            return result;
        }
        catch (error) {
            console.warn('⚠️ Gate.io futures fetch failed, using cached data');
            // Return cached data if available
            const cached = this.cache.getUltraFast('gateio_futures_fallback');
            return cached || [];
        }
    }
    /**
     * 🚀 PARALLEL: Process array in parallel batches
     */
    async processInParallel(items, processor, batchSize = 100) {
        const results = [];
        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            const batchResults = batch
                .map(processor)
                .filter((result) => result !== null);
            results.push(...batchResults);
        }
        return results;
    }
    /**
     * 🔄 NORMALIZE: Convert Gate.io spot data to standard format
     */
    normalizeSpotData(ticker) {
        try {
            const price = parseFloat(ticker.last) || 0;
            if (price <= 0)
                return null;
            return {
                symbol: ticker.currency_pair.replace('_', '/'),
                price,
                bid: parseFloat(ticker.highest_bid) || price * 0.999,
                ask: parseFloat(ticker.lowest_ask) || price * 1.001,
                volume: parseFloat(ticker.base_volume) || 0,
                change24h: parseFloat(ticker.change_percentage) || 0,
                high24h: parseFloat(ticker.high_24h) || price,
                low24h: parseFloat(ticker.low_24h) || price,
                timestamp: Date.now()
            };
        }
        catch {
            return null;
        }
    }
    /**
     * 🔄 NORMALIZE: Convert Gate.io futures data to standard format
     */
    normalizeFuturesData(ticker) {
        try {
            const price = parseFloat(ticker.last) || 0;
            if (price <= 0)
                return null;
            return {
                symbol: ticker.contract?.replace('_', '/') || '',
                price,
                bid: parseFloat(ticker.highest_bid) || 0,
                ask: parseFloat(ticker.lowest_ask) || 0,
                volume: parseFloat(ticker.base_volume) || 0,
                change24h: parseFloat(ticker.change_percentage) || 0,
                high24h: parseFloat(ticker.high_24h) || price,
                low24h: parseFloat(ticker.low_24h) || price,
                openInterest: parseFloat(ticker.total_size) || 0,
                fundingRate: parseFloat(ticker.funding_rate) || 0,
                timestamp: Date.now()
            };
        }
        catch {
            return null;
        }
    }
    /**
     * 🔐 SECURITY: Generate HMAC signature for Gate.io
     */
    generateSignature(method, path, body, timestamp) {
        const message = `${method}\n${path}\n\n${timestamp}`;
        return crypto
            .createHmac('sha512', process.env.GATEIO_SECRET_KEY || '')
            .update(message)
            .digest('hex');
    }
    /**
     * 📊 GET: Retrieve cached data
     */
    getData() {
        return this.cache.getUltraFast('gateio_worker_data');
    }
    /**
     * 📊 STATUS: Get worker status
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            lastUpdate: this.lastUpdate,
            spotPairs: this.cachedSpotData.length,
            futuresPairs: this.cachedFuturesData.length,
            totalPairs: this.cachedSpotData.length + this.cachedFuturesData.length
        };
    }
}
//# sourceMappingURL=GateioWorker.js.map