{"version": 3, "file": "GateioWorker.js", "sourceRoot": "", "sources": ["../../src/workers/GateioWorker.ts"], "names": [], "mappings": "AAAA,8EAA8E;AAC9E,qDAAqD;AAErD,OAAO,KAAwB,MAAM,OAAO,CAAC;AAC7C,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAE3D,MAAM,OAAO,YAAY;IACf,MAAM,CAAgB;IACtB,KAAK,CAAe;IACpB,SAAS,GAAY,KAAK,CAAC;IAC3B,UAAU,GAAW,CAAC,CAAC;IACvB,cAAc,GAA0B,IAAI,CAAC;IAErD,2DAA2D;IACnD,cAAc,GAAe,EAAE,CAAC;IAChC,iBAAiB,GAAkB,EAAE,CAAC;IAE9C;QACE,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;QACxC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,UAAU,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC;YACjC,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,EAAE,EAAE,wCAAwC;YACxD,cAAc,EAAE,EAAE,EAAE,oBAAoB;YACxC,OAAO,EAAE,GAAG,EAAE,qCAAqC;YACnD,iBAAiB,EAAE,KAAK,CAAC,uBAAuB;SACjD,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,uBAAuB;YAC9D,OAAO,EAAE,IAAI;YACb,UAAU;YACV,OAAO,EAAE;gBACP,YAAY,EAAE,8BAA8B;gBAC5C,QAAQ,EAAE,kBAAkB;gBAC5B,YAAY,EAAE,YAAY;aAC3B;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAE1C,gBAAgB;QAChB,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QAEvB,4DAA4D;QAC5D,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC3C,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACH,IAAI;QACF,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,SAAS;QACrB,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,qDAAqD;YACrD,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;gBAC3D,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,gBAAgB,EAAE;aACxB,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,oBAAoB;YACpB,IAAI,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBACtC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;gBACvC,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACnF,CAAC;YAED,uBAAuB;YACvB,IAAI,aAAa,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBACzC,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,KAAK,CAAC;gBAC7C,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACzF,CAAC;YAED,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACrD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,iCAAiC;YACjC,MAAM,YAAY,GAAiB;gBACjC,QAAQ,EAAE,QAAQ;gBAClB,IAAI,EAAE,IAAI,CAAC,cAAc;gBACzB,OAAO,EAAE,IAAI,CAAC,iBAAiB;gBAC/B,SAAS,EAAE,IAAI,CAAC,UAAU;gBAC1B,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,oBAAoB,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,YAAY;YAE/E,OAAO,CAAC,GAAG,CAAC,qBAAqB,SAAS,GAAG,YAAY,aAAa,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAEvG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC;YACH,0DAA0D;YAC1D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBAClC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC;gBACvC,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CACxB,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CACrD;aACF,CAAQ,CAAC;YAEV,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YAElE,gDAAgD;YAChD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;YAEvG,qCAAqC;YACrC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,sBAAsB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YAChE,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAChE,kCAAkC;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;YAC/D,OAAO,MAAM,IAAI,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,0DAA0D;YAC1D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBAClC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC;gBAC/C,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CACxB,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CACrD;aACF,CAAQ,CAAC;YAEV,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YAElE,gDAAgD;YAChD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;YAEzG,qCAAqC;YACrC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,yBAAyB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YACnE,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACnE,kCAAkC;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC;YAClE,OAAO,MAAM,IAAI,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,KAAU,EACV,SAAgC,EAChC,YAAoB,GAAG;QAEvB,MAAM,OAAO,GAAQ,EAAE,CAAC;QAExB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACjD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;YAC5C,MAAM,YAAY,GAAG,KAAK;iBACvB,GAAG,CAAC,SAAS,CAAC;iBACd,MAAM,CAAC,CAAC,MAAM,EAAe,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC;YAEpD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAW;QACnC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,KAAK,IAAI,CAAC;gBAAE,OAAO,IAAI,CAAC;YAE5B,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;gBAC9C,KAAK;gBACL,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,KAAK,GAAG,KAAK;gBACpD,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,KAAK,GAAG,KAAK;gBACnD,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;gBAC3C,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBACpD,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK;gBAC7C,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK;gBAC3C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,MAAW;QACtC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,KAAK,IAAI,CAAC;gBAAE,OAAO,IAAI,CAAC;YAE5B,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE;gBAChD,KAAK;gBACL,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;gBACxC,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;gBACvC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;gBAC3C,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBACpD,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK;gBAC7C,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK;gBAC3C,YAAY,EAAE,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;gBAChD,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;gBACjD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAc,EAAE,IAAY,EAAE,IAAY,EAAE,SAAiB;QACrF,MAAM,OAAO,GAAG,GAAG,MAAM,KAAK,IAAI,OAAO,SAAS,EAAE,CAAC;QACrD,OAAO,MAAM;aACV,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE,CAAC;aACzD,MAAM,CAAC,OAAO,CAAC;aACf,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;YACrC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM;YAC3C,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM;SACvE,CAAC;IACJ,CAAC;CACF"}