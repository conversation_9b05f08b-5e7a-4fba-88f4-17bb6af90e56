// 🚀 ULTRA-FAST MEXC Worker - Dedicated processing for maximum performance
// Target: <1s response time with intelligent caching
import axios from 'axios';
import https from 'https';
import { CacheService } from '../services/CacheService.js';
export class MexcWorker {
    spotClient;
    futuresClient;
    cache;
    isRunning = false;
    lastUpdate = 0;
    updateInterval = null;
    // 🚀 PERFORMANCE: Shared data to avoid repeated processing
    cachedSpotData = [];
    cachedFuturesData = [];
    constructor() {
        this.cache = CacheService.getInstance();
        this.initializeClients();
    }
    /**
     * 🚀 ULTRA-FAST: Initialize HTTP clients with aggressive optimization
     */
    initializeClients() {
        const httpsAgent = new https.Agent({
            keepAlive: true,
            maxSockets: 30, // Higher for MEXC (fastest exchange)
            maxFreeSockets: 15, // More free sockets
            timeout: 500, // Very fast timeout for MEXC
            freeSocketTimeout: 60000 // Longer keep-alive
        });
        const config = {
            timeout: 3000,
            httpsAgent,
            headers: {
                'User-Agent': 'Ultra-Fast-Arbitrage-Bot/1.0',
                'Accept': 'application/json',
                'Connection': 'keep-alive'
            }
        };
        // Spot client
        this.spotClient = axios.create({
            baseURL: 'https://api.mexc.com',
            ...config
        });
        // Futures client (different base URL)
        this.futuresClient = axios.create({
            baseURL: 'https://contract.mexc.com',
            ...config
        });
    }
    /**
     * 🚀 MAIN: Start continuous data fetching
     */
    async start() {
        if (this.isRunning)
            return;
        this.isRunning = true;
        console.log('🔥 MEXC Worker: Started');
        // Initial fetch
        await this.fetchData();
        // Continuous updates every 1.5 seconds (MEXC is faster)
        this.updateInterval = setInterval(async () => {
            await this.fetchData();
        }, 1500);
    }
    /**
     * 🛑 Stop worker
     */
    stop() {
        this.isRunning = false;
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        console.log('🛑 MEXC Worker: Stopped');
    }
    /**
     * 🚀 ULTRA-FAST: Fetch data with parallel processing
     */
    async fetchData() {
        const startTime = performance.now();
        try {
            // 🔥 PARALLEL: Fetch spot and futures simultaneously
            const [spotResult, futuresResult] = await Promise.allSettled([
                this.fetchSpotData(),
                this.fetchFuturesData()
            ]);
            let spotCount = 0;
            let futuresCount = 0;
            // Process spot data
            if (spotResult.status === 'fulfilled') {
                this.cachedSpotData = spotResult.value;
                spotCount = spotResult.value.length;
            }
            else {
                console.error('❌ MEXC Worker: Spot data failed:', spotResult.reason?.message);
            }
            // Process futures data
            if (futuresResult.status === 'fulfilled') {
                this.cachedFuturesData = futuresResult.value;
                futuresCount = futuresResult.value.length;
            }
            else {
                console.error('❌ MEXC Worker: Futures data failed:', futuresResult.reason?.message);
            }
            const processingTime = performance.now() - startTime;
            this.lastUpdate = Date.now();
            // 🚀 CACHE: Store processed data
            const exchangeData = {
                exchange: 'mexc',
                spot: this.cachedSpotData,
                futures: this.cachedFuturesData,
                timestamp: this.lastUpdate,
                status: 'success'
            };
            this.cache.setUltraFast('mexc_worker_data', exchangeData, true); // Hot cache
            console.log(`⚡ MEXC Worker: ${spotCount + futuresCount} pairs in ${processingTime.toFixed(1)}ms`);
        }
        catch (error) {
            console.error('❌ MEXC Worker: Critical error:', error);
        }
    }
    /**
     * 🚀 SPOT: Fetch spot data
     */
    async fetchSpotData() {
        const response = await this.spotClient.get('/api/v3/ticker/24hr');
        const tickers = Array.isArray(response.data) ? response.data : [];
        // 🚀 PARALLEL: Process tickers in larger batches for maximum speed
        return this.processInParallel(tickers, (ticker) => this.normalizeSpotData(ticker), 500);
    }
    /**
     * 🚀 FUTURES: Fetch futures data
     */
    async fetchFuturesData() {
        const response = await this.futuresClient.get('/api/v1/contract/ticker');
        // MEXC futures returns data in response.data.data format
        const responseData = response.data?.data || response.data;
        const tickers = Array.isArray(responseData) ? responseData : [];
        // 🚀 PARALLEL: Process tickers in larger batches for maximum speed
        return this.processInParallel(tickers, (ticker) => this.normalizeFuturesData(ticker), 400);
    }
    /**
     * 🚀 PARALLEL: Process array in parallel batches
     */
    async processInParallel(items, processor, batchSize = 100) {
        const results = [];
        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            const batchResults = batch
                .map(processor)
                .filter((result) => result !== null);
            results.push(...batchResults);
        }
        return results;
    }
    /**
     * 🔄 NORMALIZE: Convert MEXC spot data to standard format
     */
    normalizeSpotData(ticker) {
        try {
            const price = parseFloat(ticker.lastPrice) || 0;
            if (price <= 0)
                return null;
            let symbol = ticker.symbol || '';
            // 🔄 NORMALIZE: Convert MEXC symbol format to standard
            if (symbol.endsWith('USDT') && !symbol.includes('/')) {
                symbol = symbol.replace('USDT', '/USDT');
            }
            else if (symbol.endsWith('BTC') && !symbol.includes('/')) {
                symbol = symbol.replace('BTC', '/BTC');
            }
            else if (symbol.endsWith('ETH') && !symbol.includes('/')) {
                symbol = symbol.replace('ETH', '/ETH');
            }
            return {
                symbol,
                price,
                bid: parseFloat(ticker.bidPrice) || price * 0.999,
                ask: parseFloat(ticker.askPrice) || price * 1.001,
                volume: parseFloat(ticker.volume) || 0,
                change24h: parseFloat(ticker.priceChangePercent) || 0,
                high24h: parseFloat(ticker.highPrice) || price,
                low24h: parseFloat(ticker.lowPrice) || price,
                timestamp: Date.now()
            };
        }
        catch {
            return null;
        }
    }
    /**
     * 🔄 NORMALIZE: Convert MEXC futures data to standard format
     */
    normalizeFuturesData(ticker) {
        try {
            const price = parseFloat(ticker.lastPrice) || 0;
            if (price <= 0)
                return null;
            return {
                symbol: ticker.symbol || '',
                price,
                bid: parseFloat(ticker.bid1) || 0,
                ask: parseFloat(ticker.ask1) || 0,
                volume: parseFloat(ticker.volume24) || 0,
                change24h: parseFloat(ticker.riseFallRate) || 0,
                high24h: parseFloat(ticker.high24Price) || price,
                low24h: parseFloat(ticker.low24Price) || price,
                openInterest: parseFloat(ticker.holdVol) || 0,
                fundingRate: parseFloat(ticker.fundingRate) || 0,
                timestamp: Date.now()
            };
        }
        catch {
            return null;
        }
    }
    /**
     * 📊 GET: Retrieve cached data
     */
    getData() {
        return this.cache.getUltraFast('mexc_worker_data');
    }
    /**
     * 📊 STATUS: Get worker status
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            lastUpdate: this.lastUpdate,
            spotPairs: this.cachedSpotData.length,
            futuresPairs: this.cachedFuturesData.length,
            totalPairs: this.cachedSpotData.length + this.cachedFuturesData.length
        };
    }
}
//# sourceMappingURL=MexcWorker.js.map