// 🚀 ULTRA-FAST Worker Manager - Orchestrates all exchange workers
// Manages parallel processing and data aggregation
import { GateioWorker } from './GateioWorker.js';
import { MexcWorker } from './MexcWorker.js';
import { BitgetWorker } from './BitgetWorker.js';
import { CacheService } from '../services/CacheService.js';
import { UltraFastCache } from '../services/UltraFastCache.js';
export class WorkerManager {
    static instance;
    gateioWorker;
    mexcWorker;
    bitgetWorker;
    cache;
    ultraCache;
    isRunning = false;
    aggregationInterval = null;
    // 🚀 PERFORMANCE: Cached opportunities
    cachedOpportunities = [];
    lastAggregation = 0;
    constructor() {
        this.gateioWorker = new GateioWorker();
        this.mexcWorker = new MexcWorker();
        this.bitgetWorker = new BitgetWorker();
        this.cache = CacheService.getInstance();
        this.ultraCache = UltraFastCache.getInstance();
    }
    /**
     * 🏭 SINGLETON: Get instance
     */
    static getInstance() {
        if (!WorkerManager.instance) {
            WorkerManager.instance = new WorkerManager();
        }
        return WorkerManager.instance;
    }
    /**
     * 🚀 START: Initialize all workers
     */
    async start() {
        if (this.isRunning)
            return;
        console.log('🔥 WorkerManager: Starting all workers...');
        this.isRunning = true;
        // 🚀 PARALLEL: Start all workers simultaneously
        await Promise.allSettled([
            this.gateioWorker.start(),
            this.mexcWorker.start(),
            this.bitgetWorker.start()
        ]);
        // Start aggregation process every 1 second
        this.aggregationInterval = setInterval(async () => {
            await this.aggregateOpportunities();
        }, 1000);
        console.log('✅ WorkerManager: All workers started successfully');
    }
    /**
     * 🛑 STOP: Stop all workers
     */
    async stop() {
        if (!this.isRunning)
            return;
        console.log('🛑 WorkerManager: Stopping all workers...');
        this.isRunning = false;
        // Stop aggregation
        if (this.aggregationInterval) {
            clearInterval(this.aggregationInterval);
            this.aggregationInterval = null;
        }
        // Stop all workers
        this.gateioWorker.stop();
        this.mexcWorker.stop();
        this.bitgetWorker.stop();
        console.log('✅ WorkerManager: All workers stopped');
    }
    /**
     * 🚀 AGGREGATE: Process data from all workers and find opportunities
     */
    async aggregateOpportunities() {
        const startTime = performance.now();
        try {
            // 🚀 PARALLEL: Get data from all workers
            const [gateioData, mexcData, bitgetData] = await Promise.all([
                this.gateioWorker.getData(),
                this.mexcWorker.getData(),
                this.bitgetWorker.getData()
            ]);
            const validData = [gateioData, mexcData, bitgetData]
                .filter((data) => data !== null);
            if (validData.length === 0) {
                console.log('⚠️ WorkerManager: No valid data from workers');
                return;
            }
            // 🚀 ULTRA-FAST: Calculate opportunities
            const opportunities = this.calculateArbitrageOpportunities(validData);
            // 🚀 ULTRA-CACHE: Store opportunities in multi-layer cache
            this.cachedOpportunities = opportunities;
            this.lastAggregation = Date.now();
            const cacheData = {
                opportunities,
                timestamp: this.lastAggregation,
                totalOpportunities: opportunities.length,
                exchanges: validData.map(d => d.exchange)
            };
            // Store in both caches for redundancy
            this.cache.setUltraFast('aggregated_opportunities', cacheData, true);
            this.ultraCache.set('opportunities_hot', cacheData, 'hot'); // L1 cache
            const processingTime = performance.now() - startTime;
            console.log(`⚡ WorkerManager: ${opportunities.length} opportunities in ${processingTime.toFixed(1)}ms`);
        }
        catch (error) {
            console.error('❌ WorkerManager: Aggregation error:', error);
        }
    }
    /**
     * 🚀 CALCULATE: Find arbitrage opportunities between exchanges
     */
    calculateArbitrageOpportunities(exchangeData) {
        const opportunities = [];
        const symbolMap = new Map();
        // 🚀 OPTIMIZE: Build symbol map for fast lookup
        for (const data of exchangeData) {
            // Process spot data
            for (const spot of data.spot) {
                const symbol = this.normalizeSymbol(spot.symbol);
                if (!symbolMap.has(symbol)) {
                    symbolMap.set(symbol, []);
                }
                symbolMap.get(symbol).push({ exchange: data.exchange, spot, futures: null });
            }
            // Process futures data
            for (const futures of data.futures) {
                const symbol = this.normalizeSymbol(futures.symbol);
                if (!symbolMap.has(symbol)) {
                    symbolMap.set(symbol, []);
                }
                // Try to find existing spot entry for same exchange
                const existing = symbolMap.get(symbol).find(item => item.exchange === data.exchange && item.futures === null);
                if (existing) {
                    existing.futures = futures;
                }
                else {
                    symbolMap.get(symbol).push({ exchange: data.exchange, spot: null, futures });
                }
            }
        }
        // 🚀 CALCULATE: Find opportunities
        for (const [symbol, exchangeList] of symbolMap) {
            if (exchangeList.length < 2)
                continue;
            // Find spot-futures opportunities within same exchange
            for (const item of exchangeList) {
                if (item.spot && item.futures) {
                    const opportunity = this.calculateSpotFuturesSpread(symbol, item.exchange, item.spot, item.futures);
                    if (opportunity && opportunity.spreadPercentage >= 0.3) { // ✅ APENAS POSITIVOS
                        opportunities.push(opportunity);
                    }
                }
            }
            // Find cross-exchange opportunities
            for (let i = 0; i < exchangeList.length; i++) {
                for (let j = i + 1; j < exchangeList.length; j++) {
                    const item1 = exchangeList[i];
                    const item2 = exchangeList[j];
                    // Spot vs Spot
                    if (item1.spot && item2.spot) {
                        const opportunity = this.calculateCrossExchangeSpread(symbol, item1.exchange, item2.exchange, item1.spot, item2.spot, 'spot');
                        if (opportunity && opportunity.spreadPercentage >= 0.5) { // ✅ APENAS POSITIVOS
                            opportunities.push(opportunity);
                        }
                    }
                    // Futures vs Futures
                    if (item1.futures && item2.futures) {
                        const opportunity = this.calculateCrossExchangeSpread(symbol, item1.exchange, item2.exchange, item1.futures, item2.futures, 'futures');
                        if (opportunity && opportunity.spreadPercentage >= 0.5) { // ✅ APENAS POSITIVOS
                            opportunities.push(opportunity);
                        }
                    }
                }
            }
        }
        // 🚀 SORT: By profit potential
        return opportunities
            .sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage));
        // ✅ REMOVIDO LIMITE: Sem mais limite de 500 oportunidades
    }
    /**
     * 🔄 NORMALIZE: Standardize symbol format
     */
    normalizeSymbol(symbol) {
        return symbol.toUpperCase().replace(/[-_]/g, '/');
    }
    /**
     * 🚀 CALCULATE: Spot-Futures spread within same exchange
     */
    calculateSpotFuturesSpread(symbol, exchange, spot, futures) {
        try {
            const spread = futures.price - spot.price;
            const spreadPercentage = (spread / spot.price) * 100;
            // ✅ Calcular volumes em USDT
            const spotVolumeUSDT = spot.volume * spot.price;
            const futuresVolumeUSDT = futures.volume * futures.price;
            return {
                symbol,
                spotExchange: exchange,
                futuresExchange: exchange,
                spotPrice: spot.price,
                futuresPrice: futures.price,
                spread,
                spreadPercentage,
                volume: Math.min(spot.volume, futures.volume),
                spotVolumeUSDT, // ✅ Volume spot em USDT
                futuresVolumeUSDT, // ✅ Volume futures em USDT
                fundingRate: futures.fundingRate || 0,
                profitPotential: Math.abs(spreadPercentage),
                timestamp: Date.now(),
                dataAge: 0,
                type: 'spot-futures'
            };
        }
        catch {
            return null;
        }
    }
    /**
     * 🚀 CALCULATE: Cross-exchange spread
     */
    calculateCrossExchangeSpread(symbol, exchange1, exchange2, data1, data2, type) {
        try {
            const spread = data2.price - data1.price;
            const spreadPercentage = (spread / data1.price) * 100;
            // ✅ Calcular volumes em USDT para cross-exchange
            const spotVolumeUSDT = data1.volume * data1.price;
            const futuresVolumeUSDT = data2.volume * data2.price;
            return {
                symbol,
                spotExchange: exchange1,
                futuresExchange: exchange2,
                spotPrice: data1.price,
                futuresPrice: data2.price,
                spread,
                spreadPercentage,
                volume: Math.min(data1.volume, data2.volume),
                spotVolumeUSDT, // ✅ Volume spot em USDT
                futuresVolumeUSDT, // ✅ Volume futures em USDT
                fundingRate: type === 'futures' ? (data1.fundingRate || data2.fundingRate || 0) : 0,
                profitPotential: Math.abs(spreadPercentage),
                timestamp: Date.now(),
                dataAge: 0,
                type: type === 'spot' ? 'cross-spot' : 'cross-futures'
            };
        }
        catch {
            return null;
        }
    }
    /**
     * 📊 GET: Retrieve aggregated opportunities with ultra-fast cache
     */
    getOpportunities() {
        // 🚀 L1: Try ultra-fast cache first (1ms)
        const ultraCached = this.ultraCache.get('opportunities_hot');
        if (ultraCached) {
            return ultraCached.opportunities;
        }
        // 🚀 L2: Fallback to regular cache (10ms)
        const cached = this.cache.getUltraFast('aggregated_opportunities');
        if (cached?.opportunities) {
            // Promote to L1 cache for next access
            this.ultraCache.set('opportunities_hot', cached, 'hot');
            return cached.opportunities;
        }
        // 🚀 L3: Fallback to memory (50ms)
        return this.cachedOpportunities;
    }
    /**
     * 📊 STATUS: Get overall status
     */
    getStatus() {
        const gateioStatus = this.gateioWorker.getStatus();
        const mexcStatus = this.mexcWorker.getStatus();
        const bitgetStatus = this.bitgetWorker.getStatus();
        return {
            isRunning: this.isRunning,
            lastAggregation: this.lastAggregation,
            totalOpportunities: this.cachedOpportunities.length,
            workers: {
                gateio: gateioStatus,
                mexc: mexcStatus,
                bitget: bitgetStatus
            },
            totalPairs: gateioStatus.totalPairs + mexcStatus.totalPairs + bitgetStatus.totalPairs
        };
    }
}
//# sourceMappingURL=WorkerManager.js.map