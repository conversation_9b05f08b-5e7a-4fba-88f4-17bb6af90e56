{"version": 3, "file": "WorkerManager.js", "sourceRoot": "", "sources": ["../../src/workers/WorkerManager.ts"], "names": [], "mappings": "AAAA,mEAAmE;AACnE,mDAAmD;AAEnD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAC7C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAEjD,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAC3D,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AAE/D,MAAM,OAAO,aAAa;IAChB,MAAM,CAAC,QAAQ,CAAgB;IAC/B,YAAY,CAAe;IAC3B,UAAU,CAAa;IACvB,YAAY,CAAe;IAC3B,KAAK,CAAe;IACpB,UAAU,CAAiB;IAC3B,SAAS,GAAY,KAAK,CAAC;IAC3B,mBAAmB,GAA0B,IAAI,CAAC;IAE1D,uCAAuC;IAC/B,mBAAmB,GAA2B,EAAE,CAAC;IACjD,eAAe,GAAW,CAAC,CAAC;IAEpC;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;QACxC,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC5B,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,gDAAgD;QAChD,MAAM,OAAO,CAAC,UAAU,CAAC;YACvB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;YACzB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;YACvB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;SAC1B,CAAC,CAAC;QAEH,2CAA2C;QAC3C,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAChD,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACtC,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,mBAAmB;QACnB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACxC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAClC,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QACzB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAEzB,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAClC,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,yCAAyC;YACzC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC3D,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;gBAC3B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;gBACzB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,SAAS,GAAmB,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;iBACjE,MAAM,CAAC,CAAC,IAAI,EAAwB,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;YAEzD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;gBAC5D,OAAO;YACT,CAAC;YAED,yCAAyC;YACzC,MAAM,aAAa,GAAG,IAAI,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;YAEtE,2DAA2D;YAC3D,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC;YACzC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAElC,MAAM,SAAS,GAAG;gBAChB,aAAa;gBACb,SAAS,EAAE,IAAI,CAAC,eAAe;gBAC/B,kBAAkB,EAAE,aAAa,CAAC,MAAM;gBACxC,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;aAC1C,CAAC;YAEF,sCAAsC;YACtC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,0BAA0B,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YACrE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,WAAW;YAEvE,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,oBAAoB,aAAa,CAAC,MAAM,qBAAqB,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAE1G,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,+BAA+B,CAAC,YAA4B;QAClE,MAAM,aAAa,GAA2B,EAAE,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,GAAG,EAA2D,CAAC;QAErF,gDAAgD;QAChD,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,oBAAoB;YACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACjD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC3B,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAC5B,CAAC;gBACD,SAAS,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAChF,CAAC;YAED,uBAAuB;YACvB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACpD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC3B,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAC5B,CAAC;gBAED,oDAAoD;gBACpD,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAClD,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CACzD,CAAC;gBAEF,IAAI,QAAQ,EAAE,CAAC;oBACb,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACN,SAAS,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;gBAChF,CAAC;YACH,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,KAAK,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,IAAI,SAAS,EAAE,CAAC;YAC/C,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC;gBAAE,SAAS;YAEtC,uDAAuD;YACvD,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;gBAChC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,CACjD,MAAM,EACN,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,CACb,CAAC;oBAEF,IAAI,WAAW,IAAI,WAAW,CAAC,gBAAgB,IAAI,GAAG,EAAE,CAAC,CAAC,qBAAqB;wBAC7E,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAClC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,oCAAoC;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACjD,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC9B,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBAE9B,eAAe;oBACf,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;wBAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,4BAA4B,CACnD,MAAM,EACN,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,IAAI,EACV,MAAM,CACP,CAAC;wBAEF,IAAI,WAAW,IAAI,WAAW,CAAC,gBAAgB,IAAI,GAAG,EAAE,CAAC,CAAC,qBAAqB;4BAC7E,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBAClC,CAAC;oBACH,CAAC;oBAED,qBAAqB;oBACrB,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;wBACnC,MAAM,WAAW,GAAG,IAAI,CAAC,4BAA4B,CACnD,MAAM,EACN,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,OAAO,EACb,SAAS,CACV,CAAC;wBAEF,IAAI,WAAW,IAAI,WAAW,CAAC,gBAAgB,IAAI,GAAG,EAAE,CAAC,CAAC,qBAAqB;4BAC7E,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,OAAO,aAAa;aACjB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAC7E,0DAA0D;IAC9D,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,MAAc;QACpC,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,0BAA0B,CAChC,MAAc,EACd,QAAgB,EAChB,IAAS,EACT,OAAY;QAEZ,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAC1C,MAAM,gBAAgB,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;YAErD,6BAA6B;YAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;YAChD,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;YAEzD,OAAO;gBACL,MAAM;gBACN,YAAY,EAAE,QAAQ;gBACtB,eAAe,EAAE,QAAQ;gBACzB,SAAS,EAAE,IAAI,CAAC,KAAK;gBACrB,YAAY,EAAE,OAAO,CAAC,KAAK;gBAC3B,MAAM;gBACN,gBAAgB;gBAChB,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC;gBAC7C,cAAc,EAAE,wBAAwB;gBACxC,iBAAiB,EAAE,2BAA2B;gBAC9C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC;gBACrC,eAAe,EAAE,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBAC3C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,OAAO,EAAE,CAAC;gBACV,IAAI,EAAE,cAAc;aACrB,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,4BAA4B,CAClC,MAAc,EACd,SAAiB,EACjB,SAAiB,EACjB,KAAU,EACV,KAAU,EACV,IAAwB;QAExB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YACzC,MAAM,gBAAgB,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;YAEtD,iDAAiD;YACjD,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;YAClD,MAAM,iBAAiB,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;YAErD,OAAO;gBACL,MAAM;gBACN,YAAY,EAAE,SAAS;gBACvB,eAAe,EAAE,SAAS;gBAC1B,SAAS,EAAE,KAAK,CAAC,KAAK;gBACtB,YAAY,EAAE,KAAK,CAAC,KAAK;gBACzB,MAAM;gBACN,gBAAgB;gBAChB,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC;gBAC5C,cAAc,EAAE,wBAAwB;gBACxC,iBAAiB,EAAE,2BAA2B;gBAC9C,WAAW,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnF,eAAe,EAAE,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBAC3C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,OAAO,EAAE,CAAC;gBACV,IAAI,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe;aACvD,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,0CAA0C;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAM,mBAAmB,CAAC,CAAC;QAClE,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,WAAW,CAAC,aAAa,CAAC;QACnC,CAAC;QAED,0CAA0C;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,0BAA0B,CAAC,CAAC;QACnE,IAAI,MAAM,EAAE,aAAa,EAAE,CAAC;YAC1B,sCAAsC;YACtC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,MAAM,CAAC,aAAa,CAAC;QAC9B,CAAC;QAED,mCAAmC;QACnC,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;QACnD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;QAEnD,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM;YACnD,OAAO,EAAE;gBACP,MAAM,EAAE,YAAY;gBACpB,IAAI,EAAE,UAAU;gBAChB,MAAM,EAAE,YAAY;aACrB;YACD,UAAU,EAAE,YAAY,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,GAAG,YAAY,CAAC,UAAU;SACtF,CAAC;IACJ,CAAC;CACF"}