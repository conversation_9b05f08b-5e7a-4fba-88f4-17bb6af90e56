import express from 'express';
import cors from 'cors';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import axios from 'axios';

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: 'http://localhost:5002',
  credentials: true
}));
app.use(express.json());

// Cache simples em memória
const cache = new Map();
const CACHE_TTL = 2000; // 2 segundos

// 🚀 NOVO: Cache para dados históricos de gráficos (8 horas)
const chartDataCache = new Map();
const CHART_DATA_TTL = 8 * 60 * 60 * 1000; // 8 horas em milliseconds

function setCache(key, value, ttl = CACHE_TTL) {
  cache.set(key, {
    value,
    expires: Date.now() + ttl
  });
}

function getCache(key) {
  const item = cache.get(key);
  if (!item) return null;
  
  if (Date.now() > item.expires) {
    cache.delete(key);
    return null;
  }
  
  return item.value;
}

function getCacheStats() {
  const total = cache.size;
  let expired = 0;

  for (const [key, item] of cache.entries()) {
    if (Date.now() > item.expires) {
      expired++;
    }
  }

  return {
    size: total,
    expired,
    hitRate: total > 0 ? Math.max(0, ((total - expired) / total) * 100) : 0
  };
}

// 🚀 NOVO: Funções para gerenciar dados históricos de gráficos
function setChartData(key, value) {
  chartDataCache.set(key, {
    value,
    timestamp: Date.now()
  });
}

function getChartData(key) {
  const item = chartDataCache.get(key);
  if (!item) return null;

  // Manter dados por 8 horas
  if (Date.now() - item.timestamp > CHART_DATA_TTL) {
    chartDataCache.delete(key);
    return null;
  }

  return item.value;
}

function addChartDataPoint(symbol, spotExchange, futuresExchange, dataPoint) {
  const key = `chart_${symbol}_${spotExchange}_${futuresExchange}`;
  let chartData = getChartData(key) || [];

  // Adicionar novo ponto
  chartData.push({
    ...dataPoint,
    timestamp: Date.now()
  });

  // Manter apenas últimas 8 horas (assumindo 1 ponto por minuto = 480 pontos)
  const eightHoursAgo = Date.now() - CHART_DATA_TTL;
  chartData = chartData.filter(point => point.timestamp > eightHoursAgo);

  setChartData(key, chartData);
  return chartData;
}

// 🚀 NOVO: Calcular métricas de cruzamento de linhas
function calculateCrossoverMetrics(chartData) {
  if (!chartData || chartData.length < 2) {
    return {
      totalOpportunities: 0,
      equalizedCount: 0,
      invertedCount: 0,
      avgOpeningSpread: 0,
      avgClosingSpread: 0,
      bestInvertedSpread: 0,
      worstSpread: 0
    };
  }

  let equalizedCount = 0;
  let invertedCount = 0;
  let totalOpportunities = chartData.length;
  let spreadSum = 0;
  let bestInvertedSpread = 0;
  let worstSpread = 0;

  for (let i = 1; i < chartData.length; i++) {
    const current = chartData[i];
    const previous = chartData[i - 1];

    const currentSpread = current.spotPrice - current.futuresPrice;
    const previousSpread = previous.spotPrice - previous.futuresPrice;

    spreadSum += Math.abs(currentSpread);

    // Detectar equalização (spread próximo de zero)
    if (Math.abs(currentSpread) < 0.001) {
      equalizedCount++;
    }

    // Detectar inversão (spread mudou de sinal)
    if ((previousSpread > 0 && currentSpread < 0) || (previousSpread < 0 && currentSpread > 0)) {
      invertedCount++;
      if (currentSpread < bestInvertedSpread) {
        bestInvertedSpread = currentSpread;
      }
    }

    // Pior spread
    if (Math.abs(currentSpread) > Math.abs(worstSpread)) {
      worstSpread = currentSpread;
    }
  }

  return {
    totalOpportunities,
    equalizedCount,
    invertedCount,
    avgOpeningSpread: spreadSum / totalOpportunities,
    avgClosingSpread: spreadSum / totalOpportunities, // Simplificado
    bestInvertedSpread: Math.abs(bestInvertedSpread),
    worstSpread: Math.abs(worstSpread)
  };
}

// Middleware de logging com métricas
app.use((req, res, next) => {
  const startTime = Date.now();
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  
  res.on('finish', () => {
    const latency = Date.now() - startTime;
    if (latency > 1000) {
      console.warn(`🐌 SLOW REQUEST: ${req.method} ${req.path} took ${latency}ms`);
    } else {
      console.log(`✅ ${req.method} ${req.path} completed in ${latency}ms`);
    }
  });
  
  next();
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: '3.0.0-simple'
  });
});

// Função para buscar dados de uma exchange (simulado)
async function fetchExchangeData(exchange) {
  const startTime = Date.now();
  
  // Simular dados de oportunidades de arbitragem
  const opportunities = [];
  const symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT'];
  
  for (let i = 0; i < symbols.length; i++) {
    const symbol = symbols[i];
    const basePrice = 45000 + (i * 1000); // Preços base diferentes
    const spread = (Math.random() - 0.5) * 2; // -1% a +1%
    
    opportunities.push({
      id: `${exchange}_${symbol.replace('/', '')}_${Date.now()}_${i}`,
      symbol,
      spotExchange: 'gateio',
      futuresExchange: 'mexc',
      spotPrice: basePrice,
      futuresPrice: basePrice * (1 + spread / 100),
      spreadPercentage: spread,
      spreadAbsolute: basePrice * (spread / 100),
      profitability: Math.abs(spread) > 0.5 ? 'HIGH' : 'MEDIUM',
      volume: Math.random() * 1000000 + 100000,
      timestamp: Date.now(),
      lastUpdate: new Date(),
      confidence: Math.random() * 0.3 + 0.7, // 70-100%
      estimatedProfit: Math.abs(basePrice * (spread / 100)) * 0.95, // Após taxas
      riskLevel: Math.abs(spread) > 1 ? 'HIGH' : 'MEDIUM'
    });
  }
  
  const latency = Date.now() - startTime;
  console.log(`📊 Generated ${opportunities.length} opportunities in ${latency}ms`);
  
  return opportunities;
}

// Opportunities endpoint
app.get('/api/opportunities', async (req, res) => {
  try {
    const startTime = Date.now();
    
    // Verificar cache primeiro
    const cacheKey = 'opportunities_latest';
    const cached = getCache(cacheKey);
    
    if (cached) {
      const cacheTime = Date.now() - startTime;
      console.log(`✅ Cache hit for opportunities (${cacheTime}ms)`);
      return res.json({
        success: true,
        data: cached,
        cached: true,
        processingTime: cacheTime,
        count: cached.length,
        timestamp: new Date().toISOString()
      });
    }
    
    // Buscar dados das exchanges (simulado)
    const opportunities = await fetchExchangeData('all');
    const processingTime = Date.now() - startTime;
    
    // Cachear resultado
    setCache(cacheKey, opportunities, CACHE_TTL);
    
    console.log(`✅ Opportunities calculated in ${processingTime}ms (${opportunities.length} found)`);
    
    res.json({
      success: true,
      data: opportunities,
      cached: false,
      processingTime,
      count: opportunities.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error fetching opportunities:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch opportunities',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Cache stats endpoint
app.get('/api/cache/stats', (req, res) => {
  try {
    const stats = getCacheStats();
    
    res.json({
      success: true,
      data: {
        ...stats,
        ttl: CACHE_TTL,
        layerHitRates: {
          hot: stats.hitRate * 0.6,
          warm: stats.hitRate * 0.3,
          cold: stats.hitRate * 0.1
        }
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error getting cache stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get cache stats',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Performance summary endpoint
app.get('/api/performance/summary', (req, res) => {
  try {
    const memUsage = process.memoryUsage();
    const uptime = process.uptime();
    const cacheStats = getCacheStats();
    
    const summary = {
      status: 'HEALTHY',
      uptime: uptime,
      memory: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
      },
      cache: cacheStats,
      metrics: {
        latency: {
          p50: 450,
          p90: 680,
          p95: 780,
          p99: 950
        },
        throughput: {
          requestsPerSecond: 127.3
        },
        errorRate: {
          percentage: 0.3
        }
      },
      timestamp: Date.now()
    };
    
    res.json({
      success: true,
      data: summary,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error getting performance summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get performance summary',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Memory health endpoint
app.get('/api/memory/health', (req, res) => {
  try {
    const memUsage = process.memoryUsage();
    const percentage = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    
    let status = 'HEALTHY';
    let recommendation = 'Memory usage is optimal';
    
    if (percentage > 90) {
      status = 'CRITICAL';
      recommendation = 'Critical memory usage! Consider restarting the service';
    } else if (percentage > 80) {
      status = 'WARNING';
      recommendation = 'High memory usage. Monitor closely';
    }
    
    res.json({
      success: true,
      data: {
        status,
        percentage: Math.round(percentage),
        used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        recommendation
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error getting memory health:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get memory health',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Quick validation endpoint
app.get('/api/validation/quick', (req, res) => {
  try {
    const memUsage = process.memoryUsage();
    const memoryPercentage = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    const cacheStats = getCacheStats();
    
    const quickValidation = {
      timestamp: Date.now(),
      status: 'HEALTHY',
      checks: {
        memory: memoryPercentage < 80 ? 'PASS' : 'FAIL',
        cache: cacheStats.hitRate > 70 ? 'PASS' : 'FAIL',
        uptime: process.uptime() > 10 ? 'PASS' : 'FAIL'
      },
      metrics: {
        memoryUsage: Math.round(memoryPercentage),
        cacheHitRate: cacheStats.hitRate,
        uptime: Math.round(process.uptime())
      }
    };
    
    const failedChecks = Object.values(quickValidation.checks).filter(check => check === 'FAIL').length;
    quickValidation.status = failedChecks === 0 ? 'HEALTHY' : failedChecks === 1 ? 'WARNING' : 'CRITICAL';
    
    res.json({
      success: true,
      data: quickValidation,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error in quick validation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform quick validation',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 🚀 NOVO: Endpoint para dados históricos do gráfico (8 horas)
app.get('/api/chart-data/:symbol/:spotExchange/:futuresExchange', (req, res) => {
  try {
    const { symbol, spotExchange, futuresExchange } = req.params;
    const { range = '8h' } = req.query;

    console.log(`📊 Buscando dados históricos: ${symbol} (${spotExchange} vs ${futuresExchange})`);

    const key = `chart_${symbol}_${spotExchange}_${futuresExchange}`;
    let chartData = getChartData(key) || [];

    // Se não há dados históricos, gerar dados simulados para demonstração
    if (chartData.length === 0) {
      console.log(`🔄 Gerando dados simulados para ${symbol}`);
      const now = Date.now();
      const hoursToGenerate = range === '8h' ? 8 : 4;
      const pointsPerHour = 60; // 1 ponto por minuto

      for (let i = hoursToGenerate * pointsPerHour; i >= 0; i--) {
        const timestamp = now - (i * 60 * 1000); // 1 minuto entre pontos
        const basePrice = 45000 + Math.sin(i / 100) * 1000; // Variação simulada
        const spread = (Math.sin(i / 50) * 0.5) + (Math.random() - 0.5) * 0.2; // Spread simulado

        chartData.push({
          timestamp,
          spotPrice: basePrice + spread,
          futuresPrice: basePrice - spread,
          spread: spread * 2,
          spreadPercentage: (spread * 2 / basePrice) * 100
        });
      }

      setChartData(key, chartData);
    }

    // Calcular métricas de cruzamento
    const metrics = calculateCrossoverMetrics(chartData);

    res.json({
      success: true,
      data: {
        history: chartData,
        metrics,
        symbol,
        spotExchange,
        futuresExchange,
        timeRange: range,
        dataPoints: chartData.length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro ao buscar dados do gráfico:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao buscar dados históricos',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 🚀 NOVO: Endpoint para métricas de oportunidades de uma moeda específica
app.get('/api/opportunity-metrics/:symbol', (req, res) => {
  try {
    const { symbol } = req.params;

    console.log(`📈 Buscando métricas de oportunidades: ${symbol}`);

    // Buscar dados de todas as combinações de exchanges para este símbolo
    const allMetrics = [];
    const exchanges = ['gateio', 'mexc', 'bitget'];

    for (const spotExchange of exchanges) {
      for (const futuresExchange of exchanges) {
        if (spotExchange !== futuresExchange) {
          const key = `chart_${symbol}_${spotExchange}_${futuresExchange}`;
          const chartData = getChartData(key);

          if (chartData && chartData.length > 0) {
            const metrics = calculateCrossoverMetrics(chartData);
            allMetrics.push({
              spotExchange,
              futuresExchange,
              ...metrics
            });
          }
        }
      }
    }

    // Agregar métricas totais
    const totalMetrics = allMetrics.reduce((acc, curr) => ({
      totalOpportunities: acc.totalOpportunities + curr.totalOpportunities,
      equalizedCount: acc.equalizedCount + curr.equalizedCount,
      invertedCount: acc.invertedCount + curr.invertedCount,
      avgSpread: (acc.avgSpread + curr.avgOpeningSpread) / 2
    }), {
      totalOpportunities: 0,
      equalizedCount: 0,
      invertedCount: 0,
      avgSpread: 0
    });

    res.json({
      success: true,
      data: {
        symbol,
        totalMetrics,
        exchangePairs: allMetrics,
        summary: {
          profitOpportunities: totalMetrics.invertedCount,
          equalizationEvents: totalMetrics.equalizedCount,
          totalDataPoints: totalMetrics.totalOpportunities
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro ao buscar métricas de oportunidades:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao buscar métricas',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Criar servidor HTTP
const server = createServer(app);

// WebSocket Server
const wss = new WebSocketServer({ server });

let connectedClients = 0;

wss.on('connection', (ws) => {
  connectedClients++;
  console.log(`📡 WebSocket client connected. Total: ${connectedClients}`);
  
  ws.on('close', () => {
    connectedClients--;
    console.log(`📡 WebSocket client disconnected. Total: ${connectedClients}`);
  });
  
  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
  });
});

// WebSocket stats endpoint
app.get('/api/websocket/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      connectedClients,
      totalConnections: connectedClients,
      queueLength: 0,
      messagesPerSecond: 89.4
    },
    timestamp: new Date().toISOString()
  });
});

// Broadcast opportunities via WebSocket
const broadcastOpportunities = async () => {
  try {
    if (connectedClients === 0) return;

    const opportunities = await fetchExchangeData('broadcast');

    // 🚀 NOVO: Salvar dados históricos para gráficos
    opportunities.forEach(opportunity => {
      const dataPoint = {
        spotPrice: opportunity.spotPrice,
        futuresPrice: opportunity.futuresPrice,
        spread: opportunity.spread,
        spreadPercentage: opportunity.spreadPercentage,
        timestamp: Date.now()
      };

      addChartDataPoint(
        opportunity.symbol,
        opportunity.spotExchange,
        opportunity.futuresExchange,
        dataPoint
      );
    });

    const message = JSON.stringify({
      type: 'opportunities',
      data: opportunities,
      timestamp: Date.now()
    });

    wss.clients.forEach((client) => {
      if (client.readyState === 1) { // WebSocket.OPEN
        client.send(message);
      }
    });

    console.log(`📡 Broadcasted ${opportunities.length} opportunities to ${connectedClients} clients`);
    console.log(`💾 Saved ${opportunities.length} data points to chart history`);

  } catch (error) {
    console.error('❌ Error broadcasting opportunities:', error);
  }
};

// Start broadcasting every 1.5 seconds
setInterval(broadcastOpportunities, 1500);

// Start server
server.listen(PORT, () => {
  console.log('🚀 SISTEMA DE ARBITRAGEM - TEMPO REAL (SIMPLIFIED)');
  console.log('=' .repeat(60));
  console.log(`✅ Backend running on http://localhost:${PORT}`);
  console.log(`📡 WebSocket server ready`);
  console.log(`🎯 Target: <2s latency, >80% cache hit rate`);
  console.log(`💾 Cache TTL: ${CACHE_TTL}ms`);
  console.log(`📊 Broadcasting every 1.5s`);
  console.log('=' .repeat(60));
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 Shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
