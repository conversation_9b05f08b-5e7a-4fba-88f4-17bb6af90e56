import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { TrendingUp, TrendingDown, BarChart3, X, Maximize2, Target, Zap } from 'lucide-react';
import { useRealTimeChart } from '@/hooks/useRealTimeChart';

// 🚀 NOVO: Interface para dados do gráfico em tempo real
interface RealTimeChartDataPoint {
  timestamp: number;
  spotPrice: number;      // Preço spot (linha azul) - order book de venda
  futuresPrice: number;   // Preço futures (linha vermelha) - order book para short
  spread: number;         // Diferença entre preços
  spreadPercentage: number; // Spread em percentual
}

// 🚀 NOVO: Métricas de cruzamento de linhas
interface CrossoverMetrics {
  totalOpportunities: number;
  equalizedCount: number;    // Quantas vezes as linhas se encontraram (spread ≈ 0)
  invertedCount: number;     // Quantas vezes as linhas se cruzaram (lucro duplo)
  avgOpeningSpread: number;
  avgClosingSpread: number;
  bestInvertedSpread: number; // Melhor oportunidade de lucro duplo
  worstSpread: number;
}

// 🚀 NOVO: Props do componente de gráfico em tempo real
interface RealTimeChartProps {
  symbol: string;
  spotExchange: string;
  futuresExchange: string;
  isOpen: boolean;
  onClose: () => void;
}

export function SpreadAnalysisChart({
  symbol,
  spotExchange,
  futuresExchange,
  isOpen,
  onClose
}: RealTimeChartProps) {
  const [timeRange, setTimeRange] = useState<'1h' | '4h' | '8h'>('8h');
  const [hoveredPoint, setHoveredPoint] = useState<RealTimeChartDataPoint | null>(null);

  // 🚀 NOVO: Usar hook de tempo real
  const { data, metrics, isLoading, error, lastUpdate } = useRealTimeChart(
    symbol,
    spotExchange,
    futuresExchange,
    isOpen // Ativo apenas quando modal está aberto
  );

  // 🚀 REMOVIDO: Lógica de fetch movida para o hook useRealTimeChart

  // 🚀 NOVO: Processar dados para o gráfico de duas linhas
  const chartData = useMemo(() => {
    return data.map(point => ({
      time: new Date(point.timestamp).toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      }),
      timestamp: point.timestamp,
      'Preço Spot': point.spotPrice,      // Linha azul - onde compramos
      'Preço Futures': point.futuresPrice, // Linha vermelha - onde fazemos short
      spread: point.spread,
      spreadPercentage: point.spreadPercentage,
      // Dados originais para tooltip
      originalData: point
    }));
  }, [data]);

  // 🚀 NOVO: Tooltip customizado para mostrar diferença de spread
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      const spotPrice = data['Preço Spot'];
      const futuresPrice = data['Preço Futures'];
      const spread = data.spread;
      const spreadPercentage = data.spreadPercentage;

      // Determinar status do spread
      let spreadStatus = '';
      let spreadColor = '';
      let spreadIcon = '';

      if (Math.abs(spread) < 0.01) {
        spreadStatus = 'EQUALIZADO';
        spreadColor = 'text-yellow-600 bg-yellow-50';
        spreadIcon = '🎯';
      } else if (spread < 0) {
        spreadStatus = 'INVERTIDO (Lucro Duplo!)';
        spreadColor = 'text-green-600 bg-green-50';
        spreadIcon = '⚡';
      } else {
        spreadStatus = 'SPREAD POSITIVO';
        spreadColor = 'text-blue-600 bg-blue-50';
        spreadIcon = '📈';
      }

      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg min-w-64">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-blue-600 font-medium">💰 Spot ({spotExchange}):</span>
              <span className="font-bold text-blue-800">
                ${spotPrice?.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-red-600 font-medium">⚡ Futures ({futuresExchange}):</span>
              <span className="font-bold text-red-800">
                ${futuresPrice?.toFixed(2)}
              </span>
            </div>
            <hr className="my-2" />
            <div className="flex justify-between items-center">
              <span className="font-medium">📊 Diferença:</span>
              <span className="font-bold">
                ${Math.abs(spread)?.toFixed(4)} ({Math.abs(spreadPercentage)?.toFixed(3)}%)
              </span>
            </div>
            <div className={`text-center font-bold ${spreadColor} p-3 rounded-lg border`}>
              <div className="text-lg mb-1">{spreadIcon}</div>
              <div>{spreadStatus}</div>
              {spread < 0 && (
                <div className="text-xs mt-1 text-green-700">
                  💰 Oportunidade de arbitragem!
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-6xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <BarChart3 className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                Análise de Spread - {symbol}
              </h2>
              <p className="text-sm text-gray-600">
                {spotExchange} (Spot) ↔ {futuresExchange} (Futures)
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            {/* Seletor de tempo */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              {(['1h', '4h', '24h', '7d'] as const).map(range => (
                <button
                  key={range}
                  onClick={() => setTimeRange(range)}
                  className={`px-3 py-1 text-sm font-medium rounded transition-colors ${
                    timeRange === range
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {range}
                </button>
              ))}
            </div>

            {/* 🚀 NOVO: Status de conexão em tempo real */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-xs">
                <div className={`w-2 h-2 rounded-full ${error ? 'bg-red-500' : 'bg-green-500'} animate-pulse`}></div>
                <span className="text-gray-600">
                  {error ? 'Erro' : 'Tempo Real'}
                </span>
                {lastUpdate > 0 && (
                  <span className="text-gray-400">
                    • Atualizado {Math.floor((Date.now() - lastUpdate) / 1000)}s atrás
                  </span>
                )}
              </div>

              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* 🚀 NOVO: Métricas de Cruzamento de Linhas */}
        {metrics && (
          <div className="p-6 border-b border-gray-200">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                📊 Análise de Cruzamento - {symbol}
              </h3>
              <p className="text-sm text-gray-600">
                Monitoramento das últimas 8 horas • {spotExchange} (Spot) vs {futuresExchange} (Futures)
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <BarChart3 className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">Pontos de Dados</span>
                </div>
                <p className="text-2xl font-bold text-blue-800">{metrics.totalOpportunities}</p>
                <p className="text-xs text-blue-600">Últimas 8 horas</p>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Target className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-600">Equalizações</span>
                </div>
                <p className="text-2xl font-bold text-yellow-800">{metrics.equalizedCount}</p>
                <p className="text-xs text-yellow-600">Linhas se encontraram</p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Zap className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-600">Inversões</span>
                </div>
                <p className="text-2xl font-bold text-green-800">{metrics.invertedCount}</p>
                <p className="text-xs text-green-600">Lucro dos dois lados!</p>
              </div>
              
              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-sm font-medium text-orange-600">Melhor Invertida</span>
                </div>
                <p className="text-2xl font-bold text-orange-800">
                  {metrics.bestInvertedSpread.toFixed(3)}%
                </p>
                <p className="text-xs text-orange-600">Maior lucro</p>
              </div>
            </div>
          </div>
        )}

        {/* 🚀 NOVO: Gráfico de Duas Linhas em Tempo Real */}
        <div className="flex-1 p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <p className="ml-4 text-gray-600">Carregando dados históricos...</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="time"
                  stroke="#666"
                  fontSize={12}
                />
                <YAxis
                  stroke="#666"
                  fontSize={12}
                  tickFormatter={(value) => `$${value.toFixed(0)}`}
                />
                <Tooltip content={<CustomTooltip />} />

                {/* 🚀 LINHA AZUL: Preços Spot (Order Book de Venda - onde compramos) */}
                <Line
                  type="monotone"
                  dataKey="Preço Spot"
                  stroke="#3b82f6"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2, fill: '#ffffff' }}
                  name={`Spot ${spotExchange}`}
                />

                {/* 🚀 LINHA VERMELHA: Preços Futures (Order Book para Short) */}
                <Line
                  type="monotone"
                  dataKey="Preço Futures"
                  stroke="#ef4444"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, stroke: '#ef4444', strokeWidth: 2, fill: '#ffffff' }}
                  name={`Futures ${futuresExchange}`}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>

        {/* 🚀 NOVA: Legenda das Linhas */}
        <div className="p-6 border-t border-gray-200">
          <div className="flex items-center justify-center gap-8 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-6 h-1 bg-blue-500 rounded"></div>
              <span className="text-gray-700 font-medium">💰 Linha Azul: Preços Spot ({spotExchange})</span>
              <span className="text-xs text-gray-500">- Order book de venda (onde compramos)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-1 bg-red-500 rounded"></div>
              <span className="text-gray-700 font-medium">⚡ Linha Vermelha: Preços Futures ({futuresExchange})</span>
              <span className="text-xs text-gray-500">- Order book para short (onde vendemos)</span>
            </div>
          </div>

          {/* 🚀 NOVO: Instruções de uso */}
          <div className="mt-4 text-center text-xs text-gray-500">
            <p>💡 <strong>Hover</strong> sobre o gráfico para ver diferenças de spread • <strong>Equalização</strong> = linhas se encontram • <strong>Inversão</strong> = lucro dos dois lados</p>
          </div>
        </div>
      </div>
    </div>
  );
}