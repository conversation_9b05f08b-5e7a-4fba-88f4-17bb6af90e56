import React, { memo, useMemo, useState } from 'react';
import { useArbitrageDataOptimized } from '../../hooks/useArbitrageDataOptimized';
import { useOpportunityMetrics } from '../../hooks/useOpportunityMetrics';
import { SpreadAnalysisChart } from '../charts/SpreadAnalysisChart';
import { BarChart3, Target, Zap } from 'lucide-react';

interface Opportunity {
  id: string;
  symbol: string;
  spotPrice: number;
  futuresPrice: number;
  spread: number;
  spreadPercentage: number;
  timestamp: number;
  spotExchange: string;
  futuresExchange: string;
  spotVolumeUSDT?: number;
  futuresVolumeUSDT?: number;
  fundingRate?: number;
}

// 🚀 NOVO: Componente para métricas de oportunidades
const OpportunityMetrics = memo(({ symbol }: { symbol: string }) => {
  const { metrics, isLoading } = useOpportunityMetrics(symbol);

  if (isLoading || !metrics) {
    return (
      <div className="flex items-center gap-2 text-xs text-gray-400">
        <div className="animate-pulse">...</div>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-3 text-xs">
      <div className="flex items-center gap-1 text-yellow-600">
        <Target className="h-3 w-3" />
        <span className="font-medium">{metrics.equalizedCount}</span>
        <span className="text-gray-500">equalizações</span>
      </div>
      <div className="flex items-center gap-1 text-green-600">
        <Zap className="h-3 w-3" />
        <span className="font-medium">{metrics.invertedCount}</span>
        <span className="text-gray-500">inversões</span>
      </div>
    </div>
  );
});

const OpportunityRow = memo(({ opportunity, onOpenChart }: {
  opportunity: Opportunity;
  onOpenChart: (symbol: string, spotExchange: string, futuresExchange: string) => void;
}) => (
  <div className="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
    <div className="flex items-center justify-between">
      {/* Symbol, Chart Button and Metrics */}
      <div className="flex-shrink-0 w-80">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-bold text-gray-900">{opportunity.symbol}</h3>
            <button
              onClick={() => onOpenChart(
                opportunity.symbol,
                opportunity.spotExchange,
                opportunity.futuresExchange
              )}
              className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
              title="Ver análise de spread em tempo real"
            >
              <BarChart3 className="h-4 w-4" />
            </button>
          </div>

          {/* 🚀 NOVO: Métricas de oportunidades */}
          <OpportunityMetrics symbol={opportunity.symbol} />
        </div>
      </div>
      
      {/* Exchanges */}
      <div className="flex gap-2 flex-shrink-0 w-48">
        <span className="text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded-full font-medium">
          Spot: {opportunity.spotExchange}
        </span>
        <span className="text-xs bg-purple-100 text-purple-700 px-3 py-1 rounded-full font-medium">
          Futures: {opportunity.futuresExchange}
        </span>
      </div>
      
      {/* Preços */}
      <div className="flex items-center gap-4 flex-shrink-0 w-64">
        <div className="text-center">
          <p className="text-xs text-gray-500 mb-1">Spot</p>
          <p className="text-sm font-semibold text-blue-600">
            ${opportunity.spotPrice.toFixed(6)}
          </p>
        </div>
        <div className="text-gray-400 text-lg">→</div>
        <div className="text-center">
          <p className="text-xs text-gray-500 mb-1">Future</p>
          <p className="text-sm font-semibold text-purple-600">
            ${opportunity.futuresPrice.toFixed(6)}
          </p>
        </div>
      </div>
      
      {/* Volumes */}
      <div className="flex gap-4 text-xs text-gray-600 flex-shrink-0 w-32">
        <div className="text-center">
          <p className="text-gray-500">Vol Spot</p>
          <p className="font-medium">${((opportunity.spotVolumeUSDT || 0) / 1000).toFixed(0)}k</p>
        </div>
        <div className="text-center">
          <p className="text-gray-500">Vol Fut</p>
          <p className="font-medium">${((opportunity.futuresVolumeUSDT || 0) / 1000).toFixed(0)}k</p>
        </div>
      </div>
      
      {/* Spread */}
      <div className="text-center flex-shrink-0 w-20">
        <p className="text-xs text-gray-500 mb-1">Spread</p>
        <p className="text-xl font-bold text-green-600">
          {opportunity.spreadPercentage.toFixed(2)}%
        </p>
      </div>
      
      {/* Funding Rate */}
      <div className="text-center flex-shrink-0 w-20">
        <p className="text-xs text-gray-500 mb-1">Funding</p>
        {opportunity.fundingRate !== undefined ? (
          <p className={`text-sm font-medium ${
            opportunity.fundingRate > 0 ? 'text-red-500' : 'text-green-500'
          }`}>
            {(opportunity.fundingRate * 100).toFixed(3)}%
          </p>
        ) : (
          <p className="text-sm text-gray-400">-</p>
        )}
      </div>
      
      {/* Timestamp */}
      <div className="text-xs text-gray-500 flex-shrink-0 w-16">
        {new Date(opportunity.timestamp).toLocaleTimeString()}
      </div>
    </div>
  </div>
));

const ConnectionStatus = memo(({ isConnected, reconnectAttempts }: { 
  isConnected: boolean; 
  reconnectAttempts: number; 
}) => (
  <div className="mb-6 p-4 rounded-lg bg-gray-50">
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
        <span className="font-medium">
          {isConnected ? 'Conectado' : 'Desconectado'}
        </span>
      </div>
      {reconnectAttempts > 0 && (
        <span className="text-sm text-gray-600">
          Tentativas: {reconnectAttempts}
        </span>
      )}
    </div>
  </div>
));

const StatsCards = memo(({ opportunities }: { opportunities: Opportunity[] }) => {
  const stats = useMemo(() => {
    const total = opportunities.length;
    const avgSpread = total > 0 
      ? opportunities.reduce((sum, opp) => sum + opp.spreadPercentage, 0) / total 
      : 0;
    const maxSpread = total > 0 
      ? Math.max(...opportunities.map(opp => opp.spreadPercentage)) 
      : 0;
    
    return { total, avgSpread, maxSpread };
  }, [opportunities]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-blue-600">Total Oportunidades</h3>
        <p className="text-2xl font-bold text-blue-800">{stats.total}</p>
      </div>
      <div className="bg-green-50 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-green-600">Spread Médio</h3>
        <p className="text-2xl font-bold text-green-800">{stats.avgSpread.toFixed(2)}%</p>
      </div>
      <div className="bg-purple-50 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-purple-600">Maior Spread</h3>
        <p className="text-2xl font-bold text-purple-800">{stats.maxSpread.toFixed(2)}%</p>
      </div>
    </div>
  );
});

export const OptimizedDashboard: React.FC = memo(() => {
  const { opportunities, isLoading, error, lastUpdate, isConnected, reconnectAttempts } = useArbitrageDataOptimized();
  const [chartState, setChartState] = useState<{
    isOpen: boolean;
    symbol: string;
    spotExchange: string;
    futuresExchange: string;
  }>({
    isOpen: false,
    symbol: '',
    spotExchange: '',
    futuresExchange: ''
  });

  const handleOpenChart = (symbol: string, spotExchange: string, futuresExchange: string) => {
    setChartState({
      isOpen: true,
      symbol,
      spotExchange,
      futuresExchange
    });
  };

  const handleCloseChart = () => {
    setChartState(prev => ({ ...prev, isOpen: false }));
  };

  // Filtrar apenas oportunidades POSITIVAS e aumentar limite para 150
  const displayOpportunities = useMemo(() => 
    opportunities
      .filter(opp => opp.spreadPercentage > 0) // ✅ APENAS SPREADS POSITIVOS
      .slice(0, 150), // ✅ AUMENTADO PARA 150 OPORTUNIDADES
    [opportunities]
  );

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Erro</h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Arbitragem Spot-Futuros
          </h1>
          <p className="text-gray-600">
            Última atualização: {lastUpdate ? new Date(lastUpdate).toLocaleString() : 'Nunca'}
          </p>
        </div>

        <ConnectionStatus 
          isConnected={isConnected} 
          reconnectAttempts={reconnectAttempts} 
        />

        <StatsCards opportunities={displayOpportunities} />

        {isLoading && opportunities.length === 0 ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Contador de Oportunidades */}
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-800">
                Oportunidades de Arbitragem ({displayOpportunities.length})
              </h2>
              <div className="text-sm text-gray-600">
                Mostrando {displayOpportunities.length} de {opportunities.length} oportunidades
              </div>
            </div>
            
            {/* Header da Tabela */}
            <div className="bg-gray-100 border border-gray-200 rounded-lg p-4 font-semibold text-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex-shrink-0 w-24">Symbol</div>
                <div className="flex-shrink-0 w-48">Exchanges</div>
                <div className="flex-shrink-0 w-64">Preços</div>
                <div className="flex-shrink-0 w-32">Volumes</div>
                <div className="flex-shrink-0 w-20">Spread</div>
                <div className="flex-shrink-0 w-20">Funding</div>
                <div className="flex-shrink-0 w-16">Hora</div>
              </div>
            </div>
            
            {/* Lista de Oportunidades */}
            <div className="space-y-1 max-h-[75vh] overflow-y-auto">
              {displayOpportunities.map((opportunity) => (
                <OpportunityRow 
                  key={opportunity.id} 
                  opportunity={opportunity}
                  onOpenChart={handleOpenChart}
                />
              ))}
            </div>
          </div>
        )}

        {opportunities.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">
              Nenhuma oportunidade encontrada no momento
            </p>
          </div>
        )}

        {/* Chart Modal */}
        <SpreadAnalysisChart
          symbol={chartState.symbol}
          spotExchange={chartState.spotExchange}
          futuresExchange={chartState.futuresExchange}
          isOpen={chartState.isOpen}
          onClose={handleCloseChart}
        />
      </div>
    </div>
  );
});

OptimizedDashboard.displayName = 'OptimizedDashboard';