// 🚀 HOOK: useOpportunityMetrics - Métricas de oportunidades por moeda
// Hook para buscar quantas vezes uma moeda teve equalizações e inversões

import { useState, useEffect, useCallback } from 'react';

interface OpportunityMetrics {
  symbol: string;
  totalOpportunities: number;
  equalizedCount: number;
  invertedCount: number;
  avgSpread: number;
}

interface UseOpportunityMetricsReturn {
  metrics: OpportunityMetrics | null;
  isLoading: boolean;
  error: string | null;
}

export function useOpportunityMetrics(symbol: string): UseOpportunityMetricsReturn {
  const [metrics, setMetrics] = useState<OpportunityMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = useCallback(async () => {
    if (!symbol) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/opportunity-metrics/${symbol}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setMetrics({
          symbol,
          totalOpportunities: result.data.totalMetrics.totalOpportunities,
          equalizedCount: result.data.totalMetrics.equalizedCount,
          invertedCount: result.data.totalMetrics.invertedCount,
          avgSpread: result.data.totalMetrics.avgSpread
        });
      } else {
        throw new Error(result.error || 'Erro desconhecido');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao buscar métricas';
      console.error('❌ Erro ao buscar métricas de oportunidades:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [symbol]);

  useEffect(() => {
    fetchMetrics();
    
    // Atualizar métricas a cada 30 segundos
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, [fetchMetrics]);

  return {
    metrics,
    isLoading,
    error
  };
}
