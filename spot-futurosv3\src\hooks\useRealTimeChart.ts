// 🚀 HOOK: useRealTimeChart - Integração WebSocket + Dados Históricos
// Hook personalizado para gerenciar dados do gráfico em tempo real

import { useState, useEffect, useCallback, useRef } from 'react';
import { useWebSocketContext } from '@/contexts/WebSocketContext';

// Interface para dados do gráfico em tempo real
interface RealTimeChartDataPoint {
  timestamp: number;
  spotPrice: number;
  futuresPrice: number;
  spread: number;
  spreadPercentage: number;
}

// Interface para métricas de cruzamento
interface CrossoverMetrics {
  totalOpportunities: number;
  equalizedCount: number;
  invertedCount: number;
  avgOpeningSpread: number;
  avgClosingSpread: number;
  bestInvertedSpread: number;
  worstSpread: number;
}

// Interface para o hook
interface UseRealTimeChartReturn {
  data: RealTimeChartDataPoint[];
  metrics: CrossoverMetrics | null;
  isLoading: boolean;
  error: string | null;
  lastUpdate: number;
}

export function useRealTimeChart(
  symbol: string,
  spotExchange: string,
  futuresExchange: string,
  isActive: boolean = true
): UseRealTimeChartReturn {
  const [data, setData] = useState<RealTimeChartDataPoint[]>([]);
  const [metrics, setMetrics] = useState<CrossoverMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState(0);
  
  const { lastMessage, isConnected } = useWebSocketContext();
  const dataRef = useRef<RealTimeChartDataPoint[]>([]);
  const lastFetchRef = useRef(0);

  // 🚀 FUNÇÃO: Buscar dados históricos iniciais
  const fetchHistoricalData = useCallback(async () => {
    if (!isActive) return;

    try {
      setIsLoading(true);
      setError(null);
      
      console.log(`📊 Buscando dados históricos: ${symbol} (${spotExchange} vs ${futuresExchange})`);
      
      const response = await fetch(
        `/api/chart-data/${symbol}/${spotExchange}/${futuresExchange}?range=8h`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        const historicalData = result.data.history || [];
        setData(historicalData);
        setMetrics(result.data.metrics);
        dataRef.current = historicalData;
        setLastUpdate(Date.now());
        
        console.log(`✅ Carregados ${historicalData.length} pontos históricos`);
      } else {
        throw new Error(result.error || 'Erro desconhecido');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao buscar dados';
      console.error('❌ Erro ao buscar dados históricos:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [symbol, spotExchange, futuresExchange, isActive]);

  // 🚀 FUNÇÃO: Processar mensagens do WebSocket
  const processWebSocketMessage = useCallback((message: any) => {
    if (!message || !message.data || !Array.isArray(message.data)) return;

    const opportunities = message.data;
    let hasUpdates = false;

    opportunities.forEach((opportunity: any) => {
      // Verificar se é a moeda e exchanges corretas
      if (
        opportunity.symbol === symbol &&
        opportunity.spotExchange === spotExchange &&
        opportunity.futuresExchange === futuresExchange
      ) {
        const newDataPoint: RealTimeChartDataPoint = {
          timestamp: opportunity.timestamp || Date.now(),
          spotPrice: opportunity.spotPrice,
          futuresPrice: opportunity.futuresPrice,
          spread: opportunity.spread,
          spreadPercentage: opportunity.spreadPercentage
        };

        // Adicionar novo ponto aos dados
        dataRef.current = [...dataRef.current, newDataPoint];
        
        // Manter apenas últimas 8 horas (480 pontos assumindo 1 por minuto)
        const eightHoursAgo = Date.now() - (8 * 60 * 60 * 1000);
        dataRef.current = dataRef.current.filter(point => point.timestamp > eightHoursAgo);
        
        hasUpdates = true;
      }
    });

    if (hasUpdates) {
      setData([...dataRef.current]);
      setLastUpdate(Date.now());
      
      // Recalcular métricas se necessário
      if (dataRef.current.length > 1) {
        const newMetrics = calculateMetrics(dataRef.current);
        setMetrics(newMetrics);
      }
    }
  }, [symbol, spotExchange, futuresExchange]);

  // 🚀 FUNÇÃO: Calcular métricas de cruzamento
  const calculateMetrics = useCallback((chartData: RealTimeChartDataPoint[]): CrossoverMetrics => {
    if (chartData.length < 2) {
      return {
        totalOpportunities: 0,
        equalizedCount: 0,
        invertedCount: 0,
        avgOpeningSpread: 0,
        avgClosingSpread: 0,
        bestInvertedSpread: 0,
        worstSpread: 0
      };
    }

    let equalizedCount = 0;
    let invertedCount = 0;
    let totalOpportunities = chartData.length;
    let spreadSum = 0;
    let bestInvertedSpread = 0;
    let worstSpread = 0;

    for (let i = 1; i < chartData.length; i++) {
      const current = chartData[i];
      const previous = chartData[i - 1];
      
      const currentSpread = current.spread;
      const previousSpread = previous.spread;
      
      spreadSum += Math.abs(currentSpread);
      
      // Detectar equalização (spread próximo de zero)
      if (Math.abs(currentSpread) < 0.01) {
        equalizedCount++;
      }
      
      // Detectar inversão (spread mudou de sinal)
      if ((previousSpread > 0 && currentSpread < 0) || (previousSpread < 0 && currentSpread > 0)) {
        invertedCount++;
        if (Math.abs(currentSpread) > bestInvertedSpread) {
          bestInvertedSpread = Math.abs(currentSpread);
        }
      }
      
      // Pior spread
      if (Math.abs(currentSpread) > Math.abs(worstSpread)) {
        worstSpread = currentSpread;
      }
    }

    return {
      totalOpportunities,
      equalizedCount,
      invertedCount,
      avgOpeningSpread: spreadSum / totalOpportunities,
      avgClosingSpread: spreadSum / totalOpportunities,
      bestInvertedSpread,
      worstSpread: Math.abs(worstSpread)
    };
  }, []);

  // 🚀 EFFECT: Buscar dados históricos iniciais
  useEffect(() => {
    if (isActive) {
      fetchHistoricalData();
    }
  }, [fetchHistoricalData, isActive]);

  // 🚀 EFFECT: Processar mensagens do WebSocket
  useEffect(() => {
    if (lastMessage && isConnected && isActive) {
      processWebSocketMessage(lastMessage);
    }
  }, [lastMessage, isConnected, processWebSocketMessage, isActive]);

  // 🚀 EFFECT: Atualizar dados periodicamente (fallback)
  useEffect(() => {
    if (!isActive) return;

    const interval = setInterval(() => {
      const now = Date.now();
      // Buscar novos dados a cada 30 segundos se não houver atualizações via WebSocket
      if (now - lastFetchRef.current > 30000 && now - lastUpdate > 30000) {
        fetchHistoricalData();
        lastFetchRef.current = now;
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [fetchHistoricalData, lastUpdate, isActive]);

  return {
    data,
    metrics,
    isLoading,
    error,
    lastUpdate
  };
}
